//Auto Generate Don't Edit it
using UnityEngine;
using System;
using System.IO;
using System.Collections;
using System.Collections.Generic;
using Qarth;

namespace GameWish.Game
{
    public static partial class TDDigLevelInfoTable
    {
        private static TDTableMetaData m_MetaData = new TDTableMetaData(TDDigLevelInfoTable.Parse, "DigLevelInfo");
        public static TDTableMetaData metaData
        {
            get { return m_MetaData; }
        }
        
        private static Dictionary<int, TDDigLevelInfo> m_DataCache = new Dictionary<int, TDDigLevelInfo>();
        private static List<TDDigLevelInfo> m_DataList = new List<TDDigLevelInfo >();
        private static byte[] m_FileDataCache;
        public static byte[] fileDataCache
        {
            get { return m_FileDataCache; }
        }
        
        public static void Parse(byte[] fileData)
        {
            m_DataCache.Clear();
            m_DataList.Clear();
            m_FileDataCache = fileData;
            DataStreamReader dataR = new DataStreamReader(fileData);
            int rowCount = dataR.GetRowCount();
            int[] fieldIndex = dataR.GetFieldIndex(TDDigLevelInfo.GetFieldHeadIndex());
    #if (UNITY_STANDALONE_WIN) || UNITY_EDITOR || UNITY_STANDALONE_OSX
            dataR.CheckFieldMatch(TDDigLevelInfo.GetFieldHeadIndex(), "DigLevelInfoTable");
    #endif
            for (int i = 0; i < rowCount; ++i)
            {
                TDDigLevelInfo memberInstance = new TDDigLevelInfo();
                memberInstance.ReadRow(dataR, fieldIndex);
                OnAddRow(memberInstance);
                memberInstance.Reset();
                CompleteRowAdd(memberInstance);
            }
            Log.i(string.Format("Parse Success TDDigLevelInfo"));
        }

        private static void OnAddRow(TDDigLevelInfo memberInstance)
        {
            int key = memberInstance.id;
            if (m_DataCache.ContainsKey(key))
            {
                Log.e(string.Format("Invaild,  TDDigLevelInfoTable Id already exists {0}", key));
            }
            else
            {
                m_DataCache.Add(key, memberInstance);
                m_DataList.Add(memberInstance);
            }
        }    
        
        public static void Reload(byte[] fileData)
        {
            Parse(fileData);
        }

        public static int count
        {
            get 
            {
                return m_DataCache.Count;
            }
        }

        public static List<TDDigLevelInfo> dataList
        {
            get 
            {
                return m_DataList;
            }    
        }

        public static TDDigLevelInfo GetData(int key)
        {
            if (m_DataCache.ContainsKey(key))
            {
                return m_DataCache[key];
            }
            else
            {
                Log.w(string.Format("Can't find key {0} in TDDigLevelInfo", key));
                return null;
            }
        }
    }
}//namespace LR