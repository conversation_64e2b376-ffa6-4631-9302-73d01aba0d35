using System;
using System.Collections.Generic;
using UnityEditor;
using UnityEditorInternal;
using UnityEngine;

namespace ScriptBoy.DiggableTerrains2D
{
    [CustomEditor(typeof(PolygonTerrain2D))]
    class PolygonTerrain2DEditor : Terrain2DEditor
    {
        static class GUIContents
        {
            public static GUIContent useDelaunay = new GUIContent("Use Delaunay", "");
            public static GUIContent enableHoles = new GUIContent("Enable Holes", "Does the terrain support holes?");
            public static GUIContent enablePhysics = new GUIContent("Enable Physics");
            public static GUIContent anchors = new GUIContent("Anchors");
        }

        SerializedProperty m_UseDelaunayProp;
        SerializedProperty m_EnableHolesProp;
        SerializedProperty m_EnablePhysicsProp;
        SerializedProperty m_AnchorsProp;
        List<Vector2> m_Anchors;

        AnchorsList m_AnchorsList;
        Tool m_AnchorsTool;

        bool m_AnchorsChanged;

        protected override Type mapDataType => typeof(PolygonTerrain2DAsset);

        protected override void OnEnable()
        {
            base.OnEnable();

            m_Anchors = new List<Vector2>();
            m_AnchorsList = new AnchorsList(m_Anchors);
            m_AnchorsTool = new Tool(DrawAnchorsEditor);
        }

        protected override void OnHeaderGUI()
        {
            base.OnHeaderGUI();
            EditorGUILayout.PropertyField(m_UseDelaunayProp, GUIContents.useDelaunay);
            EditorGUILayout.PropertyField(m_EnableHolesProp, GUIContents.enableHoles);


            EditorGUI.BeginChangeCheck();
            EditorGUILayout.PropertyField(m_EnablePhysicsProp, GUIContents.enablePhysics);
            if (EditorGUI.EndChangeCheck())
            {

                Terrain2D terrain = target as Terrain2D;
                Terrain2DAsset asset = m_AssetProp.objectReferenceValue as Terrain2DAsset;
                if (asset != null)
                { 
                    serializedObject.ApplyModifiedProperties();
                    Undo.RegisterCompleteObjectUndo(asset, "Edit Terrain2DAsset");
                    terrain.WriteAsset(asset);
                    EditorUtility.SetDirty(asset);
                    EditorUtility.SetDirty(terrain);
                }
            }
        }

        protected override void OnFooterGUI()
        {
            DrawAnchorsGUI();
        }

        void DrawAnchorsGUI()
        {
            if (m_EnablePhysicsProp.boolValue)
            {
                bool foldout = m_AnchorsProp.isExpanded;
                foldout = EditorGUILayout.Foldout(foldout, GUIContents.anchors);
                m_AnchorsProp.isExpanded = foldout;
                if (foldout)
                {
                    UpdateAnchors();

                    EditorGUI.indentLevel++;
                    DrawToolButton(m_AnchorsTool, "Edit Mode");

                    string msg =
                        "To snap a handle, hold the <b>Ctrl</b> button.\n\n" +
                        "To duplicate a handle, <b>right-click</b> on it and then choose <b>Duplicate</b> from the menu.\n\n" +
                        "To delete a handle, <b>right-click</b> on it and then choose <b>Delete</b> from the menu.\n\n";


                    if (m_AnchorsTool == m_ActiveTool)
                        HelpBox.Draw(msg, 745);

                    EditorGUI.BeginChangeCheck();
                    m_AnchorsList.DoLayoutList();
                    m_AnchorsChanged |= EditorGUI.EndChangeCheck();
                    m_AnchorsChanged |= m_AnchorsProp.arraySize != m_Anchors.Count;
                    EditorGUI.indentLevel--;
                }
            }
        }


        void UpdateAnchors()
        {
            Matrix4x4 matrix = m_Terrain.transform.localToWorldMatrix;

            if (GUIUtility.hotControl == 0)
            {
                if (m_AnchorsChanged)
                {
                    m_AnchorsProp.arraySize = m_Anchors.Count;

                    m_AnchorsChanged = false;
                    for (int i = 0; i < m_Anchors.Count; i++)
                    {
                        var anchorProp = m_AnchorsProp.GetArrayElementAtIndex(i);
                        anchorProp.vector2Value = matrix.inverse.MultiplyPoint(m_Anchors[i]);
                    }
                    serializedObject.ApplyModifiedProperties();
                    Terrain2D terrain = target as Terrain2D;
                    Terrain2DAsset asset = m_AssetProp.objectReferenceValue as Terrain2DAsset;
                    if (asset != null)
                    {
                        Undo.RegisterCompleteObjectUndo(asset, "Edit Terrain2DAsset");
                        terrain.WriteAsset(asset);
                        EditorUtility.SetDirty(asset);
                        EditorUtility.SetDirty(terrain);
                    }
                }

                m_Anchors.Clear();
                for (int i = 0; i < m_AnchorsProp.arraySize; i++)
                {
                    var anchorProp = m_AnchorsProp.GetArrayElementAtIndex(i);
                    m_Anchors.Add(matrix.MultiplyPoint(anchorProp.vector2Value));
                }
            }
        }

        void DrawAnchorsEditor()
        {
            bool rightClick = Event.current.type == EventType.MouseDown && Event.current.button == 1;
            UpdateAnchors();
            for (int i = 0; i < m_Anchors.Count; i++)
            {
                Vector3 anchor = m_Anchors[i];
                float handleSize = HandleUtility.GetHandleSize(anchor) * 0.14f;
                Quaternion q = Quaternion.identity;
                EditorGUI.BeginChangeCheck();
                anchor = Handles.FreeMoveHandle(anchor, handleSize, Vector3.zero, Handles.SphereHandleCap);
                if (EditorGUI.EndChangeCheck())
                {
                    m_Anchors[i] = anchor;
                    m_AnchorsChanged = true;
                }

                if (GUIUtility.hotControl == 0 && rightClick && HandleUtility.DistanceToCircle(anchor, handleSize) < 10)
                {
                    GenericMenu menu = new GenericMenu();

                    menu.AddItem(new GUIContent("Duplicate"), false, () =>
                    {
                        m_AnchorsProp.InsertArrayElementAtIndex(i);
                        serializedObject.ApplyModifiedProperties();
                        Terrain2D terrain = target as Terrain2D;
                        Terrain2DAsset asset = m_AssetProp.objectReferenceValue as Terrain2DAsset;
                        if (asset != null)
                        {
                            Undo.RegisterCompleteObjectUndo(asset, "Edit Terrain2DAsset");
                            terrain.WriteAsset(asset);
                            EditorUtility.SetDirty(asset);
                            EditorUtility.SetDirty(terrain);
                        }
                    });

                    menu.AddItem(new GUIContent("Delete"), false, () =>
                    { 
                        m_AnchorsProp.DeleteArrayElementAtIndex(i);
                        serializedObject.ApplyModifiedProperties();
                        Terrain2D terrain = target as Terrain2D;
                        Terrain2DAsset asset = m_AssetProp.objectReferenceValue as Terrain2DAsset;
                        if (asset != null)
                        {
                            Undo.RegisterCompleteObjectUndo(asset, "Edit Terrain2DAsset");
                            terrain.WriteAsset(asset);
                            EditorUtility.SetDirty(asset);
                            EditorUtility.SetDirty(terrain);
                        }
                    });

                    menu.ShowAsContext();
                    Event.current.Use();
                    return;
                }
            }
        }



        class AnchorsList : ReorderableList
        {
            List<Vector2> m_Elements;
            public AnchorsList(List<Vector2> elements) : base(elements,typeof(Vector2), true, false, true, true)
            {
                m_Elements = elements;
                elementHeightCallback = ElementHeightCallback;
                drawElementCallback = DrawElementCallback;
            }

            float ElementHeightCallback(int index)
            {
                return EditorGUI.GetPropertyHeight(SerializedPropertyType.Vector2, GUIContent.none);
            }

            void DrawElementCallback(Rect rect, int index, bool isActive, bool isFocused)
            {
            
                EditorGUI.indentLevel++;
                m_Elements[index] = EditorGUI.Vector2Field(rect, GUIContent.none, m_Elements[index]);
                EditorGUI.indentLevel--;
            }
        }
    }
}