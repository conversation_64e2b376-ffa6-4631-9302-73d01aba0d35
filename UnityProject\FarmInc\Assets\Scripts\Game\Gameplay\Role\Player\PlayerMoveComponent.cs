using EasyCharacterMovement;
using UnityEngine;
using Qarth;

namespace GameWish.Game
{
    public class PlayerMoveComponent : EntityComponent
    {
        private string m_JoystickName;
        private float m_WalkSpdRatio;

        private Vector3 m_InputVec;
        
        public PlayerMoveComponent(string joystickName, float ratio)
        {
            m_JoystickName = joystickName;
            m_WalkSpdRatio = ratio;
        }
        
        public override void DoStart()
        {
        }

        public override void DoTick(float deltaTime)
        {
            if (UltimateJoystick.GetJoystickState(m_JoystickName))
            {
                m_InputVec = new Vector3(UltimateJoystick.GetHorizontalAxis(m_JoystickName), 0,
                    UltimateJoystick.GetVerticalAxis(m_JoystickName));
                
                if (m_InputVec != Vector3.zero)
                {
                    if (m_InputVec.magnitude < Define.PLAYER_JOYSTICK_RUN_THRESHOLD)
                    {
                        m_InputVec = m_InputVec.normalized * m_WalkSpdRatio;
                    }
                    else
                    {
                        m_InputVec = m_InputVec.normalized;
                    }
                }
                
                if (GameCamMgr.S.mainCam != null && GameCamMgr.S.mainCam.transform.localEulerAngles.y != 0)
                    m_InputVec = Quaternion.Euler(0, GameCamMgr.S.mainCam.transform.localEulerAngles.y, 0) * m_InputVec;
                (m_Owner as RoleBaseCtrller).SetMoveInput(m_InputVec);
            }
            else 
                (m_Owner as RoleBaseCtrller).SetMoveInput(Vector3.zero);
        }
    }
}