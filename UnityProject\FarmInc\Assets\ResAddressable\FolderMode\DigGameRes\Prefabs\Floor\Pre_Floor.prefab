%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &6676308523832621
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1373108536435192067}
  - component: {fileID: 7290304332220804255}
  m_Layer: 0
  m_Name: Pre_Floor
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1373108536435192067
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6676308523832621}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: -20, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1232392850318981036}
  - {fileID: 4722379494619050304}
  - {fileID: 721331029797747581}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &7290304332220804255
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6676308523832621}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d40b0d0aebbdf094b894b65bc511967e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Asset: {fileID: 0}
  m_BuildOnAwake: 1
  m_Simplification: 0.001
  m_SortingLayerID: 0
  m_SortingOrder: 2
  m_EdgeHeight: 0.2
  m_EdgeOffset: 0
  m_EdgeCornerType: 0
  m_EdgeUVMapping: 0
  m_ColliderOffset: 0.664
  m_ColliderDelaunay: 0
  m_Layers:
  - color: {r: 1, g: 1, b: 1, a: 1}
    fillColor: {r: 1, g: 1, b: 1, a: 1}
    fillTexture: {fileID: 2800000, guid: 72c0f74930ccfc04bb40e2d6aae2afe3, type: 3}
    fillUVRect:
      serializedVersion: 2
      x: -45
      y: -45
      width: 60
      height: 60
    edgeColor: {r: 0.53333336, g: 0.2901961, b: 0.03529412, a: 1}
    edgeTexture: {fileID: 0}
    edgeUVRect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 1
      height: 1
  m_SplatMapTexture: {fileID: 2800000, guid: 19a76413240ffac4b833e88b7f4db329, type: 3}
  m_SplatMapUVRect:
    serializedVersion: 2
    x: 0
    y: -25
    width: 50
    height: 50
  m_UseDefaultCheckerTexture: 0
  m_IsDiggable: 1
  m_IsFillable: 0
  m_CompressionMethod: 0
  m_CompressionLevel: 0
  m_IncludeSplatMapInSave: 0
  m_CompressSplatMapInSave: 0
  m_UseDelaunay: 0
  m_EnableHoles: 1
  m_EnablePhysics: 0
  m_Anchors:
  - {x: -7.876448, y: -3.822394}
  - {x: -3.011583, y: -5.173745}
  - {x: 0.61776006, y: -5.212355}
  - {x: 3.7837842, y: -4.6332045}
  - {x: -5.7528963, y: -4.633205}
  - {x: 8.1081085, y: -1.698842}
  - {x: 5.984556, y: -3.0501933}
--- !u!1 &2846978762646536274
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4722379494619050304}
  - component: {fileID: 8965174796053143413}
  m_Layer: 0
  m_Name: Box
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4722379494619050304
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2846978762646536274}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1373108536435192067}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &8965174796053143413
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2846978762646536274}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d02cfbf50f3296d41ad95cc91b2ae685, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Fill: 1
  m_Width: 20
  m_Height: 20
  m_CornerRadius: 0.5
  m_CornerPointCount: 0
--- !u!1 &3968660411864462669
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 721331029797747581}
  - component: {fileID: 6893842254243652580}
  m_Layer: 0
  m_Name: Box
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &721331029797747581
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3968660411864462669}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: -20, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1373108536435192067}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6893842254243652580
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3968660411864462669}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d02cfbf50f3296d41ad95cc91b2ae685, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Fill: 1
  m_Width: 20
  m_Height: 20
  m_CornerRadius: 0.5
  m_CornerPointCount: 0
--- !u!1 &8934003653658319912
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1232392850318981036}
  - component: {fileID: 3892839690980378150}
  m_Layer: 0
  m_Name: Box
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1232392850318981036
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8934003653658319912}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 20, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1373108536435192067}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &3892839690980378150
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8934003653658319912}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d02cfbf50f3296d41ad95cc91b2ae685, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Fill: 1
  m_Width: 20
  m_Height: 20
  m_CornerRadius: 0.5
  m_CornerPointCount: 0
