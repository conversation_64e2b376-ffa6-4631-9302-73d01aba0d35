using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Qarth;
using Lean.Pool;
public class DigLand : MonoBehaviour
{
	private enum PointState
	{
		die,                        //已经废弃
		wait,                       //等待检测
		collid                      //正在使用
	}

	private class PointData
	{
		public Vector2 pos;               //点位置
		public PointState status;          //点状态
		public GameObject col;          //点实例
	}

	public Vector2 scale = new Vector2(1, 1);
	public Vector2 area = new Vector2(1, 1);
	private List<PointData> listPoint = new List<PointData>();
	public List<SpriteEraser> spriteErasers;
	public float ColliderSize = 0.1f;
	public float Circle1Range1 = 1f;
	public float Circle1Range2 = 2f;
	private GameObject m_PreCollider;


	public void CreatePoint()
	{
		m_PreCollider = DigGameMgr.S.preCollider;
		int line = (int)(scale.x * 50);
		int row = (int)(scale.y * 50);
		float distancesX = area.x / line;
		float distancesY = area.y / row;

		for (int j = 0; j < row; j++)
		{
			for (int k = 0; k < line; k++)
			{
				Vector2 v2 = new Vector2(distancesX * k - 0.5f, distancesY * j - 0.5f);
				PointData data = new PointData();
				data.pos = v2;
				data.status = PointState.wait;
				listPoint.Add(data);
			}
		}
		distancesX *= 3;
		distancesY *= 3;
		for (float x = -0.5f; x <= 0.5; x += distancesX)
		{
			CreateSideCollider(new Vector3(x, -0.5f, 0));
			CreateSideCollider(new Vector3(x, 0.5f, 0));
			// CreateSideCollider(new Vector3(-0.5f, x, 0));
			// CreateSideCollider(new Vector3(0.5f, x, 0));
		}
	}

	public void CreateSideCollider(Vector3 v3)
	{
		GameObject go = LeanPool.Spawn(m_PreCollider, Vector2.zero, Quaternion.Euler(Vector3.zero), this.transform) as GameObject;
		go.transform.localPosition = v3;
		go.transform.localScale = new Vector3(ColliderSize / scale.x, ColliderSize / scale.y, ColliderSize);
		PointData data = new PointData();
		data.pos = new Vector2(v3.x, v3.y);
		data.status = PointState.collid;
		data.col = go;
		listPoint.Add(data);
	}

	public bool Dig(Vector3 hit)
	{
		//从世界坐标转为局部坐标
		Vector2 localCenter = this.transform.InverseTransformPoint(hit);
		// //如果坐标y轴和land y轴的距离超过0.5，就返回false
		// if (Mathf.Abs(localCenter.y) > 0.5f)
		// {
		// 	return false;
		// }
		for (int i = 0; i < listPoint.Count; i++)
		{
			Vector2 centerPos = listPoint[i].pos - localCenter;
			centerPos.x *= scale.x;
			centerPos.y *= scale.y;
			float dis = Vector2.Distance(centerPos, Vector2.zero);
			if (dis < Circle1Range1 && listPoint[i].status != PointState.die)
			{
				listPoint[i].status = PointState.die;
				if (listPoint[i].col != null)
				{
					LeanPool.Despawn(listPoint[i].col);
				}
			}
			else if (dis >= Circle1Range1 && dis < Circle1Range2 && listPoint[i].status == PointState.wait)
			{
				listPoint[i].status = PointState.collid;
				GameObject go = LeanPool.Spawn(m_PreCollider, Vector2.zero, Quaternion.Euler(Vector3.zero), this.transform);
				go.transform.localPosition = listPoint[i].pos;
				go.transform.localScale = new Vector3(ColliderSize / scale.x, ColliderSize / scale.y, ColliderSize);
				listPoint[i].col = go;
			}
			else if (listPoint[i].status == PointState.collid)
			{
				//如果点超过了范围 直接删除
				if (Mathf.Abs(listPoint[i].pos.y) > 0.5f)
				{
					listPoint[i].status = PointState.die;
					if (listPoint[i].col != null)
					{
						LeanPool.Despawn(listPoint[i].col);
					}
				}
			}
		}
		bool canCreatePiece = false;
		for (int i = 0; i < spriteErasers.Count; i++)
		{
			if (spriteErasers[i].DoEraser())
			{
				canCreatePiece = true;
			}
		}
		return canCreatePiece;
	}
}
