using UnityEngine;
using ScriptBoy.DiggableTerrains2D;
using System.Collections.Generic;

/// <summary>
/// 地形挖掘调试工具
/// 用于诊断多地形挖掘问题
/// </summary>

namespace GameWish.Game
{
	public class TerrainDigDebugger : MonoBehaviour
	{
	    [Header("Debug Settings")]
	    public bool enableDebugLog = true;
	    public bool showTerrainBounds = true;
	    public bool showDigPosition = true;
	    public Color boundsColor = Color.red;
	    public Color digPositionColor = Color.yellow;
	    
	    [Header("Runtime Info")]
	    [SerializeField] private int totalTerrainCount = 0;
	    [SerializeField] private int diggableTerrainCount = 0;
	    [SerializeField] private List<string> terrainInfo = new List<string>();
	    
	    private Vector3 lastDigPosition;
	    private bool isDigging = false;
	    
	    void Start()
	    {
	        // 延迟检查，确保所有地形都已初始化
	        Invoke(nameof(AnalyzeTerrains), 1f);
	    }
	    
	    void Update()
	    {
	        if (DigGameMgr.S != null && DigGameMgr.S.diggerHead != null)
	        {
	            Vector3 currentPos = DigGameMgr.S.diggerHead.transform.position;
	            bool currentlyDigging = DigGameMgr.S.diggerHead.digging;
	            
	            if (currentlyDigging && !isDigging)
	            {
	                // 开始挖掘
	                lastDigPosition = currentPos;
	                if (enableDebugLog)
	                {
	                    DebugDigAtPosition(currentPos);
	                }
	            }
	            
	            isDigging = currentlyDigging;
	        }
	    }
	    
	    [ContextMenu("Analyze Terrains")]
	    public void AnalyzeTerrains()
	    {
	        terrainInfo.Clear();
	        
	        // 获取所有地形实例
	        var allTerrains = Terrain2D.instances;
	        var polygonTerrains = PolygonTerrain2D.instances;
	        
	        totalTerrainCount = allTerrains.Length;
	        diggableTerrainCount = 0;
	        
	        Debug.Log($"=== 地形分析报告 ===");
	        Debug.Log($"总地形数量: {totalTerrainCount}");
	        Debug.Log($"多边形地形数量: {polygonTerrains.Length}");
	        
	        for (int i = 0; i < allTerrains.Length; i++)
	        {
	            var terrain = allTerrains[i];
	            string info = AnalyzeTerrain(terrain, i);
	            terrainInfo.Add(info);
	            
	            if (terrain.isDiggable)
	                diggableTerrainCount++;
	        }
	        
	        Debug.Log($"可挖掘地形数量: {diggableTerrainCount}");
	        
	        // 测试Layer Mask查找
	        TestLayerMaskFiltering();
	    }
	    
	    private string AnalyzeTerrain(Terrain2D terrain, int index)
	    {
	        string info = $"地形 {index}: {terrain.name}";
	        info += $"\n  - 类型: {terrain.GetType().Name}";
	        info += $"\n  - 位置: {terrain.transform.position}";
	        info += $"\n  - Layer: {terrain.gameObject.layer} ({LayerMask.LayerToName(terrain.gameObject.layer)})";
	        info += $"\n  - Tag: {terrain.tag}";
	        info += $"\n  - 可挖掘: {terrain.isDiggable}";
	        info += $"\n  - 可填充: {terrain.isFillable}";
	        info += $"\n  - 已构建: {terrain.isBuilt}";
	        info += $"\n  - 激活状态: {terrain.isActiveAndEnabled}";
	        
	        if (enableDebugLog)
	        {
	            Debug.Log(info);
	        }
	        
	        return info;
	    }
	    
	    private void TestLayerMaskFiltering()
	    {
	        Debug.Log("=== Layer Mask 过滤测试 ===");
	        
	        // 测试不同的Layer Mask
	        int[] testMasks = { -1, 0, 1 << 8, 1 << 21 }; // -1(全部), 0(无), DigLevel层, Ground层
	        
	        foreach (int mask in testMasks)
	        {
	            var filteredTerrains = PolygonTerrain2D.FindByMask(mask, false);
	            Debug.Log($"Layer Mask {mask}: 找到 {filteredTerrains.Length} 个地形");
	            
	            foreach (var terrain in filteredTerrains)
	            {
	                Debug.Log($"  - {terrain.name} (Layer: {terrain.gameObject.layer})");
	            }
	        }
	    }
	    
	    private void DebugDigAtPosition(Vector3 position)
	    {
	        Debug.Log($"=== 挖掘位置调试: {position} ===");
	        
	        // 检查哪些地形包含这个位置
	        var allTerrains = PolygonTerrain2D.FindByMask(-1, false);
	        int containingTerrains = 0;
	        
	        foreach (var terrain in allTerrains)
	        {
	            // 检查地形边界
	            Bounds bounds = GetTerrainBounds(terrain);
	            bool inBounds = bounds.Contains(position);
	            
	            Debug.Log($"地形 {terrain.name}:");
	            Debug.Log($"  - 边界: {bounds}");
	            Debug.Log($"  - 包含挖掘点: {inBounds}");
	            Debug.Log($"  - 可挖掘: {terrain.isDiggable}");
	            Debug.Log($"  - Layer: {terrain.gameObject.layer}");
	            
	            if (inBounds && terrain.isDiggable)
	            {
	                containingTerrains++;
	            }
	        }
	        
	        Debug.Log($"包含挖掘点的可挖掘地形数量: {containingTerrains}");
	        
	        if (containingTerrains == 0)
	        {
	            Debug.LogWarning("没有地形包含当前挖掘位置！这可能是问题所在。");
	        }
	        else if (containingTerrains == 1)
	        {
	            Debug.LogWarning("只有一个地形包含挖掘位置，这解释了为什么只能在一块地形上挖掘。");
	        }
	    }
	    
	    private Bounds GetTerrainBounds(Terrain2D terrain)
	    {
	        // 尝试从渲染器获取边界
	        var renderers = terrain.GetComponentsInChildren<Renderer>();
	        if (renderers.Length > 0)
	        {
	            Bounds bounds = renderers[0].bounds;
	            for (int i = 1; i < renderers.Length; i++)
	            {
	                bounds.Encapsulate(renderers[i].bounds);
	            }
	            return bounds;
	        }
	        
	        // 如果没有渲染器，使用碰撞器
	        var colliders = terrain.GetComponentsInChildren<Collider2D>();
	        if (colliders.Length > 0)
	        {
	            return colliders[0].bounds;
	        }
	        
	        // 最后使用变换位置
	        return new Bounds(terrain.transform.position, Vector3.one);
	    }
	    
	    void OnDrawGizmos()
	    {
	        if (!showTerrainBounds && !showDigPosition) return;
	        
	        // 绘制地形边界
	        if (showTerrainBounds)
	        {
	            var terrains = PolygonTerrain2D.FindByMask(-1, false);
	            Gizmos.color = boundsColor;
	            
	            foreach (var terrain in terrains)
	            {
	                if (terrain.isDiggable)
	                {
	                    Bounds bounds = GetTerrainBounds(terrain);
	                    Gizmos.DrawWireCube(bounds.center, bounds.size);
	                    
	                    // 绘制地形名称
	                    #if UNITY_EDITOR
	                    UnityEditor.Handles.Label(bounds.center, terrain.name);
	                    #endif
	                }
	            }
	        }
	        
	        // 绘制挖掘位置
	        if (showDigPosition && isDigging)
	        {
	            Gizmos.color = digPositionColor;
	            Gizmos.DrawWireSphere(lastDigPosition, 0.5f);
	        }
	    }
	    
	    [ContextMenu("Force Analyze Current Dig Position")]
	    public void ForceAnalyzeCurrentDigPosition()
	    {
	        if (DigGameMgr.S != null && DigGameMgr.S.diggerHead != null)
	        {
	            Vector3 pos = DigGameMgr.S.diggerHead.transform.position;
	            DebugDigAtPosition(pos);
	        }
	    }
	}
	
}