//Auto Generate Don't Edit it
using UnityEngine;
using System;
using System.IO;
using System.Collections;
using System.Collections.Generic;
using Qarth;

namespace GameWish.Game
{
    public static partial class TDDigUpgradeInfoTable
    {
        private static TDTableMetaData m_MetaData = new TDTableMetaData(TDDigUpgradeInfoTable.Parse, "DigUpgradeInfo");
        public static TDTableMetaData metaData
        {
            get { return m_MetaData; }
        }
        
        private static Dictionary<int, TDDigUpgradeInfo> m_DataCache = new Dictionary<int, TDDigUpgradeInfo>();
        private static List<TDDigUpgradeInfo> m_DataList = new List<TDDigUpgradeInfo >();
        private static byte[] m_FileDataCache;
        public static byte[] fileDataCache
        {
            get { return m_FileDataCache; }
        }
        
        public static void Parse(byte[] fileData)
        {
            m_DataCache.Clear();
            m_DataList.Clear();
            m_FileDataCache = fileData;
            DataStreamReader dataR = new DataStreamReader(fileData);
            int rowCount = dataR.GetRowCount();
            int[] fieldIndex = dataR.GetFieldIndex(TDDigUpgradeInfo.GetFieldHeadIndex());
    #if (UNITY_STANDALONE_WIN) || UNITY_EDITOR || UNITY_STANDALONE_OSX
            dataR.CheckFieldMatch(TDDigUpgradeInfo.GetFieldHeadIndex(), "DigUpgradeInfoTable");
    #endif
            for (int i = 0; i < rowCount; ++i)
            {
                TDDigUpgradeInfo memberInstance = new TDDigUpgradeInfo();
                memberInstance.ReadRow(dataR, fieldIndex);
                OnAddRow(memberInstance);
                memberInstance.Reset();
                CompleteRowAdd(memberInstance);
            }
            Log.i(string.Format("Parse Success TDDigUpgradeInfo"));
        }

        private static void OnAddRow(TDDigUpgradeInfo memberInstance)
        {
            int key = memberInstance.id;
            if (m_DataCache.ContainsKey(key))
            {
                Log.e(string.Format("Invaild,  TDDigUpgradeInfoTable Id already exists {0}", key));
            }
            else
            {
                m_DataCache.Add(key, memberInstance);
                m_DataList.Add(memberInstance);
            }
        }    
        
        public static void Reload(byte[] fileData)
        {
            Parse(fileData);
        }

        public static int count
        {
            get 
            {
                return m_DataCache.Count;
            }
        }

        public static List<TDDigUpgradeInfo> dataList
        {
            get 
            {
                return m_DataList;
            }    
        }

        public static TDDigUpgradeInfo GetData(int key)
        {
            if (m_DataCache.ContainsKey(key))
            {
                return m_DataCache[key];
            }
            else
            {
                Log.w(string.Format("Can't find key {0} in TDDigUpgradeInfo", key));
                return null;
            }
        }
    }
}//namespace LR