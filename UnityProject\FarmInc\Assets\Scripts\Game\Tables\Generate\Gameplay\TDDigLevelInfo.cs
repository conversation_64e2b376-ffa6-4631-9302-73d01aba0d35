//Auto Generate Don't Edit it
using UnityEngine;
using System;
using System.IO;
using System.Collections;
using System.Collections.Generic;
using Qarth;

namespace GameWish.Game
{
    public partial class TDDigLevelInfo
    {
        
       
        private EInt m_Id = 0;   
        private EInt m_MineRewards = 0;   
        private EInt m_DiggerPower = 0;   
        private EInt m_CleanerPower = 0;   
        private EInt m_CrystalCount = 0;   
        private EInt m_CrystalValue = 0;   
        private EInt m_SuperMineCount = 0;   
        private EInt m_SuperMineValue = 0;  
        
        //private Dictionary<string, TDUniversally.FieldData> m_DataCacheNoGenerate = new Dictionary<string, TDUniversally.FieldData>();
      
        /// <summary>
        /// ID
        /// </summary>
        public  int  id {get { return m_Id; } }
       
        /// <summary>
        /// 矿物奖励值
        /// </summary>
        public  int  mineRewards {get { return m_MineRewards; } }
       
        /// <summary>
        /// 阻力值
        /// </summary>
        public  int  diggerPower {get { return m_DiggerPower; } }
       
        /// <summary>
        /// 重量值
        /// </summary>
        public  int  cleanerPower {get { return m_CleanerPower; } }
       
        /// <summary>
        /// 水晶数量
        /// </summary>
        public  int  crystalCount {get { return m_CrystalCount; } }
       
        /// <summary>
        /// 水晶奖励值
        /// </summary>
        public  int  crystalValue {get { return m_CrystalValue; } }
       
        /// <summary>
        /// 超级矿石数量
        /// </summary>
        public  int  superMineCount {get { return m_SuperMineCount; } }
       
        /// <summary>
        /// 超级矿石奖励值
        /// </summary>
        public  int  superMineValue {get { return m_SuperMineValue; } }
       

        public void ReadRow(DataStreamReader dataR, int[] filedIndex)
        {
          //var schemeNames = dataR.GetSchemeName();
          int col = 0;
          while(true)
          {
            col = dataR.MoreFieldOnRow();
            if (col == -1)
            {
              break;
            }
            switch (filedIndex[col])
            { 
            
                case 0:
                    m_Id = dataR.ReadInt();
                    break;
                case 1:
                    m_MineRewards = dataR.ReadInt();
                    break;
                case 2:
                    m_DiggerPower = dataR.ReadInt();
                    break;
                case 3:
                    m_CleanerPower = dataR.ReadInt();
                    break;
                case 4:
                    m_CrystalCount = dataR.ReadInt();
                    break;
                case 5:
                    m_CrystalValue = dataR.ReadInt();
                    break;
                case 6:
                    m_SuperMineCount = dataR.ReadInt();
                    break;
                case 7:
                    m_SuperMineValue = dataR.ReadInt();
                    break;
                default:
                    //TableHelper.CacheNewField(dataR, schemeNames[col], m_DataCacheNoGenerate);
                    break;
            }
          }

        }
        
        public static Dictionary<string, int> GetFieldHeadIndex()
        {
          Dictionary<string, int> ret = new Dictionary<string, int>(8);
          
          ret.Add("Id", 0);
          ret.Add("MineRewards", 1);
          ret.Add("DiggerPower", 2);
          ret.Add("CleanerPower", 3);
          ret.Add("CrystalCount", 4);
          ret.Add("CrystalValue", 5);
          ret.Add("SuperMineCount", 6);
          ret.Add("SuperMineValue", 7);
          return ret;
        }
    } 
}//namespace LR