using System;
using UnityEditor;
using UnityEngine;

namespace ScriptBoy.DiggableTerrains2D
{
    [CustomEditor(typeof(VoxelTerrain2D))]
    class VoxelTerrain2DEditor : Terrain2DEditor
    {
        Tool m_MapRectTool;

        SerializedProperty m_MapWidthProp;
        SerializedProperty m_MapHeightProp;
        SerializedProperty m_MapTransformProp;
        SerializedProperty m_MapPaddingProp;
        SerializedProperty m_MapPositionProp;
        SerializedProperty m_MapScaleProp;

        protected override Type mapDataType => typeof(VoxelTerrain2DAsset);

        protected override void OnEnable()
        {
            base.OnEnable();

            m_MapRectTool = new Tool(EditMapRect);
        }

        protected override void OnFooterGUI()
        {
            bool foldout = m_MapScaleProp.isExpanded;
            foldout = EditorGUILayout.Foldout(foldout, new GUIContent("Voxel Map"));
            m_MapScaleProp.isExpanded = foldout;

            if (foldout)
            {
                bool autoTransform = m_MapTransformProp.enumValueIndex == 0;

                if (!autoTransform)
                {
                    DrawToolButton(m_MapRectTool, "Transform");
                }

                EditorGUI.indentLevel++;

                EditorGUI.BeginChangeCheck();
                EditorGUILayout.PropertyField(m_MapWidthProp, new GUIContent("Width"));
                EditorGUILayout.PropertyField(m_MapHeightProp, new GUIContent("Height"));
                if (EditorGUI.EndChangeCheck())
                {
                    Terrain2DAsset asset = m_AssetProp.objectReferenceValue as Terrain2DAsset;
                    if (asset != null)
                    {
                        Undo.RegisterCompleteObjectUndo(asset, "Clear Terrain2DAsset");
                        asset.Clear();
                        EditorUtility.SetDirty(asset);
                        EditorUtility.SetDirty(target);
                    }
                }

                EditorGUILayout.PropertyField(m_MapTransformProp, new GUIContent("Transform"));

                if (autoTransform)
                {
                    EditorGUILayout.PropertyField(m_MapPaddingProp, new GUIContent("Padding"));
                }
                else
                {


                    if (GUIUtility.hotControl == 0)
                    {
                        if (m_MapTransformChanged)
                        {
                            m_MapTransformChanged = false;
                            Vector2 mapPosition = m_MapPositionProp.vector2Value;
                            float mapScale = m_MapScaleProp.floatValue;

                            m_MapPositionProp.vector2Value = m_MapPosition;
                            m_MapScaleProp.floatValue = m_MapScale;

                            Terrain2DAsset asset = m_AssetProp.objectReferenceValue as Terrain2DAsset;
                            if (asset != null)
                            {
                                Terrain2D terrain = target as Terrain2D;
                                serializedObject.ApplyModifiedPropertiesWithoutUndo();
                                Undo.RegisterCompleteObjectUndo(asset, "Terrain2DAsset Changed");
                                terrain.WriteAsset(asset);
                                m_MapPositionProp.vector2Value = mapPosition;
                                m_MapScaleProp.floatValue = mapScale;
                                serializedObject.ApplyModifiedPropertiesWithoutUndo();
                                terrain.Build();
                                EditorUtility.SetDirty(asset);
                                EditorUtility.SetDirty(terrain);
                            }
                            else
                            {
                                GUI.changed = true;
                            }
                        }


                        m_MapPosition = m_MapPositionProp.vector2Value;
                        m_MapScale = m_MapScaleProp.floatValue;
                    }

                    EditorGUI.BeginChangeCheck();
                    m_MapPosition = EditorGUILayout.Vector2Field(new GUIContent("Position"), m_MapPosition);
                    m_MapScale = EditorGUILayout.FloatField(new GUIContent("Scale"), m_MapScale);
                    if (EditorGUI.EndChangeCheck())
                    {
                        m_MapTransformChanged = true;
                    }
                }

                EditorGUI.indentLevel--;
            }
        }

        protected override void OnSceneGUI(SceneView scene)
        {
            base.OnSceneGUI(scene);
            //DrawMapBounds();
        }

        Vector2 m_MapPosition;
        float m_MapScale;
        bool m_MapTransformChanged;


        void EditMapRect()
        {

            if (m_MapTransformProp.enumValueIndex == 0)
            {
                m_ActiveTool = null;
                return;
            }


            if (GUIUtility.hotControl == 0)
            {
                if (m_MapTransformChanged)
                {
                    m_MapTransformChanged = false;
                    Vector2 mapPosition = m_MapPositionProp.vector2Value;
                    float mapScale = m_MapScaleProp.floatValue;

                    m_MapPositionProp.vector2Value = m_MapPosition;
                    m_MapScaleProp.floatValue = m_MapScale;
       
                    Terrain2DAsset asset = m_AssetProp.objectReferenceValue as Terrain2DAsset;
                    if (asset != null)
                    {
                        Terrain2D terrain = target as Terrain2D;
                        serializedObject.ApplyModifiedPropertiesWithoutUndo();
                        Undo.RegisterCompleteObjectUndo(asset, "Terrain2DAsset Changed");
                        terrain.WriteAsset(asset);
                        m_MapPositionProp.vector2Value = mapPosition;
                        m_MapScaleProp.floatValue = mapScale;
                        serializedObject.ApplyModifiedPropertiesWithoutUndo();
                        terrain.Build();
                        EditorUtility.SetDirty(asset);
                        EditorUtility.SetDirty(terrain);
                    }
                    else
                    {
                        GUI.changed = true;
                    }
                }


                m_MapPosition = m_MapPositionProp.vector2Value;
                m_MapScale = m_MapScaleProp.floatValue;
            }

            int w = m_MapWidthProp.enumValueFlag;
            int h = m_MapHeightProp.enumValueFlag;
            float raitoX = w > h ? 1 : (float)h / w;
            float raitoY = w < h ? 1 : (float)w / h;

            Vector2 mapSize = new Vector2(m_MapScale, m_MapScale);
            mapSize = new Vector2(m_MapScale / raitoX, m_MapScale / raitoY);

            if (DoRectHandale(ref m_MapPosition, ref mapSize))
            {
                m_MapScale = Mathf.Max(mapSize.x * raitoX, mapSize.y * raitoY);
                m_MapTransformChanged = true;
            }
        }

        private void DrawMapBounds()
        {
            int w = m_MapWidthProp.enumValueFlag;
            int h = m_MapHeightProp.enumValueFlag;
            float scale = m_MapScaleProp.floatValue;
            float raitoX = w > h ? 1 : (float)h / w;
            float raitoY = w < h ? 1 : (float)w / h;
            Vector2 position = m_MapPositionProp.vector2Value;
            Vector2 size = new Vector2(scale / raitoX, scale / raitoY);

            DrawBounds(m_Terrain.transform, position, size);
        }

        void DrawBounds(Transform transform, Vector2 min, Vector2 size)
        {
            Vector3 a = new Vector3(min.x, min.y);
            Vector3 b = new Vector3(min.x + size.x, min.y);
            Vector3 c = new Vector3(min.x, min.y + size.y);
            Vector3 d = new Vector3(min.x + size.x, min.y + size.y);

            a = transform.TransformPoint(a);
            b = transform.TransformPoint(b);
            c = transform.TransformPoint(c);
            d = transform.TransformPoint(d);

            Handles.DrawAAPolyLine(2, a, b, d, c, a);
        }

        void DrawBounds(Vector2 min, Vector2 size, Color color)
        {
            using (new Handles.DrawingScope(color))
            {
                Vector3 a = new Vector3(min.x, min.y);
                Vector3 b = new Vector3(min.x + size.x, min.y);
                Vector3 c = new Vector3(min.x, min.y + size.y);
                Vector3 d = new Vector3(min.x + size.x, min.y + size.y);
                Handles.DrawPolyLine(a, b, d, c, a);
            }
        }
    }
}