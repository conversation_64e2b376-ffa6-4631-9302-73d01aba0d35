using UnityEngine;
using System.Collections;

public class SpriteEraser : MonoBehaviour
{
	public SpriteRenderer targetSprite;
	public float eraserRadius = 0.5f;
	[Header("3D Effect Settings")]
	public Color shadowColor = new Color(0.3f, 0.3f, 0.3f, 0.8f); // 阴影颜色
	public float shadowWidth = 2f; // 阴影宽度（像素）
	public bool enable3DEffect = true; // 是否启用立体效果

	private Texture2D originalTexture;
	private Texture2D editableTexture;
	private Sprite originalSprite;
	private Sprite editableSprite;



	void Start()
	{
		// 保存原始精灵
		originalSprite = targetSprite.sprite;
		originalTexture = originalSprite.texture;

		// 创建可编辑的纹理副本，使用RGBA32格式确保支持SetPixels
		editableTexture = new Texture2D(
			originalTexture.width,
			originalTexture.height,
			TextureFormat.RGBA32,
			false
		);

		// 获取原始纹理的像素数据
		Color[] pixels;
		try
		{
			// 尝试直接获取像素
			pixels = originalTexture.GetPixels();
		}
		catch (UnityException)
		{
			// 如果原始纹理不可读，创建一个默认的白色纹理
			pixels = new Color[originalTexture.width * originalTexture.height];
			for (int i = 0; i < pixels.Length; i++)
			{
				pixels[i] = Color.white;
			}
			Debug.LogWarning("Original texture is not readable. Using default white texture instead.");
		}

		// 设置像素并应用
		editableTexture.SetPixels(pixels);
		editableTexture.Apply();

		// 应用新纹理到精灵
		Rect rect = new Rect(0, 0, editableTexture.width, editableTexture.height);
		Vector2 pivot = originalSprite.pivot / originalSprite.rect.size;
		editableSprite = Sprite.Create(
			editableTexture,
			rect,
			pivot,
			originalSprite.pixelsPerUnit,
			0,
			SpriteMeshType.Tight,
			originalSprite.border
		);

		targetSprite.sprite = editableSprite;
	}

	public bool DoEraser()
	{
		if (DigGameMgr.S.diggerHead.state == DiggerState.Dig)
		{
			// 擦除像素
			// 将鼠标位置转换为世界坐标
			Vector3 worldPos = DigGameMgr.S.mainCam.ScreenToWorldPoint(DigGameMgr.S.mainCam.WorldToScreenPoint(DigGameMgr.S.diggerHead.transform.position));
			worldPos.z = 0;
			//判断坐标在不在sprite范围内,或者坐标距离sprite边缘的距离小于擦除半径
			if (targetSprite.bounds.Contains(worldPos))
			{
				return ErasePixels(worldPos, eraserRadius);
			}
		}
		return false;
	}

	public bool ErasePixels(Vector2 worldPosition, float radius)
	{
		// 将世界坐标转换为精灵局部坐标
		Vector2 localPos = targetSprite.transform.InverseTransformPoint(worldPosition);

		// 获取精灵的边界和尺寸信息
		Bounds bounds = editableSprite.bounds;

		// 将局部坐标转换为纹理UV坐标
		// 注意: 精灵的局部坐标原点在中心点，而UV坐标原点在左下角
		Vector2 uv = new Vector2(
			(localPos.x - bounds.min.x) / bounds.size.x,
			(localPos.y - bounds.min.y) / bounds.size.y
		);

		// 确保UV坐标在[0,1]范围内
		uv.x = Mathf.Clamp01(uv.x);
		uv.y = Mathf.Clamp01(uv.y);

		// 计算纹理像素坐标
		int x = Mathf.RoundToInt(uv.x * editableTexture.width);
		int y = Mathf.RoundToInt(uv.y * editableTexture.height);

		// 计算擦除半径（像素单位）
		int radiusPixels = Mathf.RoundToInt(radius * editableTexture.width / bounds.size.x);
		bool canCreatePiece = false;

		// 存储被擦除的像素位置，用于后续添加立体效果
		System.Collections.Generic.List<Vector2Int> erasedPixels = new System.Collections.Generic.List<Vector2Int>();

		// 擦除圆形区域内的像素
		for (int i = -radiusPixels; i <= radiusPixels; i++)
		{
			for (int j = -radiusPixels; j <= radiusPixels; j++)
			{
				if (i * i + j * j <= radiusPixels * radiusPixels)
				{
					int pixelX = x + i;
					int pixelY = y + j;

					if (pixelX >= 0 && pixelX < editableTexture.width &&
						pixelY >= 0 && pixelY < editableTexture.height)
					{
						if (editableTexture.GetPixel(pixelX, pixelY).a > 0)
						{
							// 将像素设置为完全透明
							editableTexture.SetPixel(pixelX, pixelY, Color.clear);
							erasedPixels.Add(new Vector2Int(pixelX, pixelY));
							canCreatePiece = true;
						}
					}
				}
			}
		}

		// 如果启用立体效果，添加阴影
		if (enable3DEffect && erasedPixels.Count > 0)
		{
			Add3DEffect(erasedPixels, x, y);
		}

		// 应用纹理更改
		editableTexture.Apply();
		return canCreatePiece;
	}

	/// <summary>
	/// 添加立体效果：在擦除边缘的右侧添加阴影
	/// </summary>
	/// <param name="erasedPixels">被擦除的像素位置列表</param>
	/// <param name="centerX">擦除中心X坐标</param>
	/// <param name="centerY">擦除中心Y坐标</param>
	private void Add3DEffect(System.Collections.Generic.List<Vector2Int> erasedPixels, int centerX, int centerY)
	{
		// 对每个被擦除的像素，检查其边缘并添加阴影
		foreach (Vector2Int erasedPixel in erasedPixels)
		{
			// 检查该像素是否在擦除区域的边缘
			if (IsEdgePixel(erasedPixel.x, erasedPixel.y))
			{
				// 计算从擦除中心到当前像素的方向向量
				Vector2 direction = new Vector2(erasedPixel.x - centerX, erasedPixel.y - centerY).normalized;

				// 计算切线方向（垂直于径向方向）
				Vector2 tangent = new Vector2(-direction.y, direction.x);

				// 计算法线方向（指向右侧）
				Vector2 normal = new Vector2(tangent.y, -tangent.x);

				// 在法线方向添加阴影
				AddShadowInDirection(erasedPixel.x, erasedPixel.y, normal);
			}
		}
	}

	/// <summary>
	/// 检查指定像素是否为边缘像素（相邻有非透明像素）
	/// </summary>
	private bool IsEdgePixel(int x, int y)
	{
		// 检查8个方向的相邻像素
		for (int dx = -1; dx <= 1; dx++)
		{
			for (int dy = -1; dy <= 1; dy++)
			{
				if (dx == 0 && dy == 0) continue;

				int checkX = x + dx;
				int checkY = y + dy;

				if (checkX >= 0 && checkX < editableTexture.width &&
					checkY >= 0 && checkY < editableTexture.height)
				{
					// 如果相邻像素不透明，则当前像素是边缘像素
					if (editableTexture.GetPixel(checkX, checkY).a > 0)
					{
						return true;
					}
				}
			}
		}
		return false;
	}

	/// <summary>
	/// 在指定方向添加阴影效果
	/// </summary>
	private void AddShadowInDirection(int startX, int startY, Vector2 direction)
	{
		int shadowWidthPixels = Mathf.RoundToInt(shadowWidth);

		for (int i = 1; i <= shadowWidthPixels; i++)
		{
			int shadowX = startX + Mathf.RoundToInt(direction.x * i);
			int shadowY = startY + Mathf.RoundToInt(direction.y * i);

			if (shadowX >= 0 && shadowX < editableTexture.width &&
				shadowY >= 0 && shadowY < editableTexture.height)
			{
				Color currentPixel = editableTexture.GetPixel(shadowX, shadowY);

				// 只在透明像素上添加阴影
				if (currentPixel.a == 0)
				{
					// 计算阴影强度（距离越远，阴影越淡）
					float intensity = 1f - (float)i / shadowWidthPixels;
					Color shadowWithIntensity = new Color(
						shadowColor.r,
						shadowColor.g,
						shadowColor.b,
						shadowColor.a * intensity
					);

					editableTexture.SetPixel(shadowX, shadowY, shadowWithIntensity);
				}
				else
				{
					// 如果遇到非透明像素，停止在这个方向添加阴影
					break;
				}
			}
		}
	}

	// 重置纹理到原始状态
	public void ResetTexture()
	{
		// 获取原始纹理的像素数据
		Color[] pixels;
		try
		{
			pixels = originalTexture.GetPixels();
		}
		catch (UnityException)
		{
			// 如果原始纹理不可读，创建一个默认的白色纹理
			pixels = new Color[originalTexture.width * originalTexture.height];
			for (int i = 0; i < pixels.Length; i++)
			{
				pixels[i] = Color.white;
			}
		}

		editableTexture.SetPixels(pixels);
		editableTexture.Apply();
	}

	// // 调试方法：在场景中显示点击位置
	// void OnDrawGizmos()
	// {
	// 	if (Application.isPlaying && Input.GetMouseButton(0))
	// 	{
	// 		Vector3 worldPos = Camera.main.ScreenToWorldPoint(Input.mousePosition);
	// 		worldPos.z = 0;

	// 		Gizmos.color = Color.red;
	// 		Gizmos.DrawWireSphere(worldPos, eraserRadius);

	// 		// 转换为精灵局部坐标
	// 		Vector2 localPos = targetSprite.transform.InverseTransformPoint(worldPos);

	// 		// 转换为UV坐标
	// 		Bounds bounds = editableSprite.bounds;
	// 		Vector2 uv = new Vector2(
	// 			(localPos.x - bounds.min.x) / bounds.size.x,
	// 			(localPos.y - bounds.min.y) / bounds.size.y
	// 		);

	// 		// 转换为像素坐标
	// 		int x = Mathf.RoundToInt(uv.x * editableTexture.width);
	// 		int y = Mathf.RoundToInt(uv.y * editableTexture.height);

	// 		//			Debug.Log($"World: {worldPos}, Local: {localPos}, UV: {uv}, Pixel: ({x}, {y})");
	// 	}
	// }
}