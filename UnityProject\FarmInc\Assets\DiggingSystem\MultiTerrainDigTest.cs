using UnityEngine;
using ScriptBoy.DiggableTerrains2D;
using System.Collections.Generic;

/// <summary>
/// 多地形挖掘测试脚本
/// 用于验证修复后的多地形挖掘功能
/// </summary>

namespace GameWish.Game
{
	public class MultiTerrainDigTest : MonoBehaviour
	{
	    [Header("Test Settings")]
	    public bool enableAutoTest = false;
	    public float testInterval = 2f;
	    public Vector2 testPosition = Vector2.zero;
	    public float testRadius = 1f;
	    
	    [Header("Debug Info")]
	    [SerializeField] private List<string> testResults = new List<string>();
	    
	    private float nextTestTime = 0f;
	    
	    void Update()
	    {
	        if (enableAutoTest && Time.time >= nextTestTime)
	        {
	            nextTestTime = Time.time + testInterval;
	            RunDigTest();
	        }
	    }
	    
	    [ContextMenu("Run Single Dig Test")]
	    public void RunDigTest()
	    {
	        testResults.Clear();
	        
	        Debug.Log("=== 开始多地形挖掘测试 ===");
	        
	        // 获取所有地形
	        var allTerrains = PolygonTerrain2D.FindByMask(-1, false);
	        Debug.Log($"找到 {allTerrains.Length} 个地形");
	        
	        if (allTerrains.Length == 0)
	        {
	            Debug.LogError("没有找到任何地形！");
	            return;
	        }
	        
	        // 记录挖掘前的状态
	        Dictionary<PolygonTerrain2D, float> beforeAreas = new Dictionary<PolygonTerrain2D, float>();
	        foreach (var terrain in allTerrains)
	        {
	            float area = GetTerrainArea(terrain);
	            beforeAreas[terrain] = area;
	            Debug.Log($"挖掘前 - {terrain.name}: 面积 = {area:F3}");
	        }
	        
	        // 执行挖掘
	        Vector2[] digPolygon = CreateCirclePolygon(testPosition, testRadius, 20);
	        
	        float totalDugArea = 0f;
	        int affectedTerrains = 0;
	        
	        foreach (var terrain in allTerrains)
	        {
	            if (!terrain.isDiggable) continue;
	            
	            float dugArea = terrain.EditByPolygon(digPolygon, false);
	            if (dugArea > 0.001f)
	            {
	                affectedTerrains++;
	                totalDugArea += dugArea;
	                
	                string result = $"✅ {terrain.name}: 挖掘面积 = {dugArea:F3}";
	                testResults.Add(result);
	                Debug.Log(result);
	            }
	            else
	            {
	                string result = $"❌ {terrain.name}: 未挖掘 (面积 = {dugArea:F3})";
	                testResults.Add(result);
	                Debug.Log(result);
	            }
	        }
	        
	        // 验证结果
	        Debug.Log($"=== 测试结果 ===");
	        Debug.Log($"总挖掘面积: {totalDugArea:F3}");
	        Debug.Log($"受影响地形数量: {affectedTerrains}/{allTerrains.Length}");
	        
	        if (affectedTerrains > 1)
	        {
	            Debug.Log("🎉 多地形挖掘成功！");
	        }
	        else if (affectedTerrains == 1)
	        {
	            Debug.LogWarning("⚠️ 只有一个地形被挖掘，可能仍有问题");
	        }
	        else
	        {
	            Debug.LogError("❌ 没有地形被挖掘！");
	        }
	        
	        // 记录挖掘后的状态
	        foreach (var terrain in allTerrains)
	        {
	            float afterArea = GetTerrainArea(terrain);
	            float beforeArea = beforeAreas[terrain];
	            float difference = beforeArea - afterArea;
	            
	            Debug.Log($"挖掘后 - {terrain.name}: 面积 = {afterArea:F3}, 变化 = {difference:F3}");
	        }
	    }
	    
	    private float GetTerrainArea(PolygonTerrain2D terrain)
	    {
	        // 尝试通过反射获取私有方法GetAllArea
	        var method = terrain.GetType().GetMethod("GetAllArea", 
	            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
	        
	        if (method != null)
	        {
	            return (float)method.Invoke(terrain, null);
	        }
	        
	        // 如果无法获取，返回估算值
	        var renderers = terrain.GetComponentsInChildren<Renderer>();
	        if (renderers.Length > 0)
	        {
	            float totalArea = 0f;
	            foreach (var renderer in renderers)
	            {
	                Bounds bounds = renderer.bounds;
	                totalArea += bounds.size.x * bounds.size.y;
	            }
	            return totalArea;
	        }
	        
	        return 0f;
	    }
	    
	    private Vector2[] CreateCirclePolygon(Vector2 center, float radius, int segments)
	    {
	        Vector2[] points = new Vector2[segments];
	        float angleStep = 2f * Mathf.PI / segments;
	        
	        for (int i = 0; i < segments; i++)
	        {
	            float angle = i * angleStep;
	            points[i] = center + new Vector2(
	                Mathf.Cos(angle) * radius,
	                Mathf.Sin(angle) * radius
	            );
	        }
	        
	        return points;
	    }
	    
	    [ContextMenu("Test Coordinate Conversion")]
	    public void TestCoordinateConversion()
	    {
	        Debug.Log("=== 坐标转换测试 ===");
	        
	        var terrains = PolygonTerrain2D.FindByMask(-1, false);
	        Vector2 worldPoint = testPosition;
	        
	        foreach (var terrain in terrains)
	        {
	            Vector2 localPoint = terrain.transform.InverseTransformPoint(worldPoint);
	            Vector2 backToWorld = terrain.transform.TransformPoint(localPoint);
	            
	            Debug.Log($"{terrain.name}:");
	            Debug.Log($"  世界坐标: {worldPoint}");
	            Debug.Log($"  本地坐标: {localPoint}");
	            Debug.Log($"  转换回世界坐标: {backToWorld}");
	            Debug.Log($"  转换误差: {Vector2.Distance(worldPoint, backToWorld):F6}");
	        }
	    }
	    
	    void OnDrawGizmos()
	    {
	        // 绘制测试位置
	        Gizmos.color = Color.red;
	        Gizmos.DrawWireSphere(testPosition, testRadius);
	        
	        // 绘制地形边界
	        var terrains = PolygonTerrain2D.FindByMask(-1, false);
	        foreach (var terrain in terrains)
	        {
	            if (terrain.isDiggable)
	            {
	                Gizmos.color = Color.green;
	            }
	            else
	            {
	                Gizmos.color = Color.gray;
	            }
	            
	            var renderers = terrain.GetComponentsInChildren<Renderer>();
	            foreach (var renderer in renderers)
	            {
	                Bounds bounds = renderer.bounds;
	                Gizmos.DrawWireCube(bounds.center, bounds.size);
	            }
	        }
	    }
	}
	
}