﻿using System;
using System.Collections;
using System.Collections.Generic;
using DG.Tweening;
using UnityEngine;
using UnityEngine.UI;
using Qarth;

namespace GameWish.Game
{
    public class DigGamingPanel : AbstractPanel
    {
        [SerializeField] private Button m_BtnChange;
        [SerializeField] private Button m_BtnBackHome;
        [SerializeField] private Text m_TxtDistanceShow;

        protected override void OnUIInit()
        {
            base.OnUIInit();

            m_BtnChange.onClick.AddListener(OnClickChange);
            m_BtnBackHome.onClick.AddListener(OnClickBackHome);


        }



        void OnClickChange()
        {
            if (DigGameMgr.S.diggerHead.state == DiggerState.Dig)
            {
                DigGameMgr.S.diggerHead.Change2Cleaner();

            }
            else
            {
                DigGameMgr.S.diggerHead.Change2Dig();
            }
        }
        void OnClickBackHome()
        {
            FarmMgr.S.QuitDigGame();
        }

        protected override void OnPanelOpen(params object[] args)
        {
            base.OnPanelOpen(args);
        }


        protected override void OnClose()
        {
            base.OnClose();

        }
    }
}