using System.Collections.Generic;
using Qarth;
using UnityEngine;
using System;
using System.Collections;
using GameWish.Game.Traffic;
using UnityEngine.SceneManagement;

namespace GameWish.Game
{
    public partial class FarmMgr
    {
        public void LoadLittleGame()
        {
            FarmGuideMgr.S.HideAllArrow();
            // LoadGrassGame();
            LoadDigGame();
        }

        public void QuitGrassGame(Action quit2Main = null)
        {
            UIMgr.S.ClosePanelAsUIID(UIID.GrassGamingPanel);
            StartCoroutine(LoadLumberMap(quit2Main));
        }
        public void QuitDigGame(Action quit2Main = null)
        {
            GameCamMgr.S.mainCam.orthographic = false;
            DigGameMgr.S.QuitGame();
            UIMgr.S.ClosePanelAsUIID(UIID.DigGamingPanel);
            StartCoroutine(LoadLumberMapFromDig(quit2Main));
        }

        private IEnumerator LoadLumberMap(Action quit2Main = null)
        {
            UIMgr.S.OpenTopPanel(UIID.CrossPanel, null);
            SceneManager.UnloadSceneAsync("GrassGame");
            yield return new WaitForSeconds(0.2f);
            Resources.UnloadUnusedAssets();
            GC.Collect();
            AddressableResMgr.S.ReleaseAllCachedAssets();
            m_LoadPhase = -1;
            CheckPreloadPahase();
            yield return new WaitUntil(() => m_LoadPhase == 5);
            UIMgr.S.ClosePanelAsUIID(UIID.CrossPanel);
            gameMode = LumberGameMode.Normal;
            quit2Main?.Invoke();
        }
        private IEnumerator LoadLumberMapFromDig(Action quit2Main = null)
        {

            UIMgr.S.OpenTopPanel(UIID.CrossPanel, null);
            SceneManager.UnloadSceneAsync("DigScene");
            yield return new WaitForSeconds(0.2f);
            Resources.UnloadUnusedAssets();
            GC.Collect();
            AddressableResMgr.S.ReleaseAllCachedAssets();
            m_LoadPhase = -1;
            CheckPreloadPahase();
            yield return new WaitUntil(() => m_LoadPhase == 5);
            UIMgr.S.ClosePanelAsUIID(UIID.CrossPanel);
            gameMode = LumberGameMode.Normal;
            quit2Main?.Invoke();
        }

        private void LoadGrassGame()
        {
            System.Action loadedAction = () =>
            {
                StartCoroutine(LoadGrassGameWorld());
            };
            UIMgr.S.OpenTopPanel(UIID.CrossPanel, null, loadedAction);
        }

        private void LoadDigGame()
        {
            System.Action loadedAction = () =>
            {
                StartCoroutine(LoadDigGameWorld());
            };
            UIMgr.S.OpenTopPanel(UIID.CrossPanel, null, loadedAction);
        }



        private IEnumerator LoadGrassGameWorld()
        {
            RailwayMgr.S.PauseMine();
            yield return new WaitForSeconds(0.2f);
            m_Player.transform.position = WorldInfoMgr.data.mapData.curMapIndex == 0
                ? new Vector3(-4.75f, 0, -9.5f)
                : new Vector3(7, 0f, -7);

            yield return new WaitForSeconds(0.2f);

            //删除所有entity
            EntityMgr.S.CleanEntities();
            yield return new WaitForSeconds(0.2f);

            //清理mgr的数据结构缓存
            VehicleMgr.S.CleanVehicles();
            EmployeeMgr.S.CleanEmployeeCache();
            CleanBuildingCache();
            CleanForestCache();

            //删除临时奖励
            BonusMgr.S.CleanBox();
            PlayerInfoMgr.data.bonusData.ResetBonusTimeByOvertime();


            yield return new WaitForSeconds(0.3f);
            //暂停update
            m_LoadPhase = -1;
            //删除世界场景
            GameObject.Destroy(m_TrsWorldRoot.gameObject);
            Resources.UnloadUnusedAssets();
            GC.Collect();
            AddressableResMgr.S.ReleaseAllCachedAssets();
            //清理存档
            WorldInfoMgr.data.CleanCurrentWorldData();
            PackMgr.S.DealPlayerData();
            //清理加载的资源
            ResetLoadItemRes();
            yield return new WaitForSeconds(0.1f);
            //加载新场景
            var loadLocker = false;
            StartCoroutine(LoadLittleGameWorldAsync(() => { loadLocker = true; }));
            yield return new WaitUntil(() => loadLocker == true);

            // yield return StartCoroutine(PathFindMgr.S.AsyncScanWorld(null, 1f));
            yield return null;
            //  TrafficMgr.S.RestructureSystem();
            yield return new WaitForSeconds(0.1f);

            EntityMgr.S.Tick(Time.deltaTime);
            EventSystem.S.Send(EventID.OnSceneLoaded);
            DataAnalysisMgr.S.CustomEvent(Define.EVENT_ENTER_NEW_WORLD, DAPE.ThinkingData);
            gameMode = LumberGameMode.LittleGame;
            //   重新触发一次引导
            //GuideMgr.S.StartGuideTrack();
            yield return new WaitForSeconds(0.1f);
            EventSystem.S.Send(EventID.OnLittleGameLoadFinish);
            UIMgr.S.OpenPanel(UIID.GrassGamingPanel);
        }
        private IEnumerator LoadDigGameWorld()
        {
            RailwayMgr.S.PauseMine();
            yield return new WaitForSeconds(0.2f);
            m_Player.transform.position = WorldInfoMgr.data.mapData.curMapIndex == 0
                ? new Vector3(-4.75f, 0, -9.5f)
                : new Vector3(7, 0f, -7);

            yield return new WaitForSeconds(0.2f);

            //删除所有entity
            EntityMgr.S.CleanEntities();
            yield return new WaitForSeconds(0.2f);

            //清理mgr的数据结构缓存
            VehicleMgr.S.CleanVehicles();
            EmployeeMgr.S.CleanEmployeeCache();
            CleanBuildingCache();
            CleanForestCache();

            //删除临时奖励
            BonusMgr.S.CleanBox();
            PlayerInfoMgr.data.bonusData.ResetBonusTimeByOvertime();


            yield return new WaitForSeconds(0.3f);
            //暂停update
            m_LoadPhase = -1;
            //删除世界场景
            GameObject.Destroy(m_TrsWorldRoot.gameObject);
            Resources.UnloadUnusedAssets();
            GC.Collect();
            AddressableResMgr.S.ReleaseAllCachedAssets();
            //清理存档
            WorldInfoMgr.data.CleanCurrentWorldData();
            PackMgr.S.DealPlayerData();
            //清理加载的资源
            ResetLoadItemRes();
            yield return new WaitForSeconds(0.1f);
            //加载新场景
            var loadLocker = false;
            StartCoroutine(LoadDigLittleGameWorldAsync(() => { loadLocker = true; }));
            yield return new WaitUntil(() => loadLocker == true);

            // yield return StartCoroutine(PathFindMgr.S.AsyncScanWorld(null, 1f));
            yield return null;
            //  TrafficMgr.S.RestructureSystem();
            yield return new WaitForSeconds(0.1f);

            EntityMgr.S.Tick(Time.deltaTime);

            DataAnalysisMgr.S.CustomEvent(Define.EVENT_ENTER_NEW_WORLD, DAPE.ThinkingData);
            gameMode = LumberGameMode.LittleGame;
            //   重新触发一次引导
            yield return new WaitForSeconds(0.1f);
            EventSystem.S.Send(EventID.OnLittleGameLoadFinish);
            yield return new WaitForSeconds(0.5f);
            EventSystem.S.Send(EventID.OnSceneLoaded);
            UIMgr.S.OpenPanel(UIID.DigGamingPanel);
            DigGameMgr.S.StartGame();
        }

        IEnumerator LoadLittleGameWorldAsync(Action callback)
        {
            SceneManager.UnloadSceneAsync("World");
            AddressableResMgr.S.LoadSceneAsync("GrassGame", (si, state) =>
            {
                if (state)
                {
                    SceneManager.SetActiveScene(si.Scene);
                    callback.Invoke();
                }
            }, LoadSceneMode.Additive);

            yield return null;

            EventSystem.S.Send(EventID.OnLittleGameLoaded);
        }
        IEnumerator LoadDigLittleGameWorldAsync(Action callback)
        {
            SceneManager.UnloadSceneAsync("World");
            AddressableResMgr.S.LoadSceneAsync("DigScene", (si, state) =>
            {
                if (state)
                {
                    SceneManager.SetActiveScene(si.Scene);
                    callback.Invoke();
                }
            }, LoadSceneMode.Additive);

            yield return null;


            EventSystem.S.Send(EventID.OnLittleGameLoaded);
        }

    }
}