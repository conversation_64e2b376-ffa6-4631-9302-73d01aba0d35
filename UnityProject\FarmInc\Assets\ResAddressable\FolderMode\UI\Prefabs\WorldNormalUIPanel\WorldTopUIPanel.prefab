%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &330179266984994649
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1903267906605356960}
  - component: {fileID: 1289866815593072194}
  - component: {fileID: 3629989064751808041}
  m_Layer: 5
  m_Name: imgGem
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1903267906605356960
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 330179266984994649}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1.1, y: 1.1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 429116095948613835}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 3000, y: -3000}
  m_SizeDelta: {x: 44, y: 36}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &1289866815593072194
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 330179266984994649}
  m_CullTransparentMesh: 1
--- !u!114 &3629989064751808041
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 330179266984994649}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: 0734e28a60539da4e90cdf60aa273ac7, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!1 &673657723270193497
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4098903935982410686}
  - component: {fileID: 5251273894785946401}
  - component: {fileID: 9095737149107458089}
  - component: {fileID: 2273840784227367915}
  - component: {fileID: 5371276230787473187}
  m_Layer: 5
  m_Name: text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &4098903935982410686
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 673657723270193497}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7508783757578546784}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 165, y: 60}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &5251273894785946401
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 673657723270193497}
  m_CullTransparentMesh: 1
--- !u!114 &9095737149107458089
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 673657723270193497}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f7201a12d95ffc409449d95f23cf332, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 12800000, guid: face6506aacc38d4b8694ecaf79f258c, type: 3}
    m_FontSize: 24
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 2
    m_MaxSize: 24
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: "\u7ECF\u9A8C+1231232"
--- !u!114 &2273840784227367915
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 673657723270193497}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e19747de3f5aca642ab2be37e372fb86, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_EffectColor: {r: 0.19607845, g: 0.3372549, b: 0.027450982, a: 1}
  m_EffectDistance: {x: 1.5, y: -1.5}
  m_UseGraphicAlpha: 1
--- !u!114 &5371276230787473187
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 673657723270193497}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 45facfdc6a639f041b007c036dc527b8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _gradientType: 1
  _blendMode: 2
  _modifyVertices: 0
  _offset: -0.077
  _zoom: 1
  _effectGradient:
    serializedVersion: 2
    key0: {r: 0.08627451, g: 0.654902, b: 0.07450981, a: 1}
    key1: {r: 0.427451, g: 0.9450981, b: 0.15294118, a: 1}
    key2: {r: 0, g: 0, b: 0, a: 0}
    key3: {r: 0, g: 0, b: 0, a: 0}
    key4: {r: 0, g: 0, b: 0, a: 0}
    key5: {r: 0, g: 0, b: 0, a: 0}
    key6: {r: 0, g: 0, b: 0, a: 0}
    key7: {r: 0, g: 0, b: 0, a: 0}
    ctime0: 0
    ctime1: 65535
    ctime2: 0
    ctime3: 0
    ctime4: 0
    ctime5: 0
    ctime6: 0
    ctime7: 0
    atime0: 0
    atime1: 65535
    atime2: 0
    atime3: 0
    atime4: 0
    atime5: 0
    atime6: 0
    atime7: 0
    m_Mode: 0
    m_ColorSpace: -1
    m_NumColorKeys: 2
    m_NumAlphaKeys: 2
--- !u!1 &1634312484200799507
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7508783757578546784}
  m_Layer: 5
  m_Name: root
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &7508783757578546784
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1634312484200799507}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4098903935982410686}
  m_Father: {fileID: 1443900031349891642}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 100, y: 100}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &1669593903101958263
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7043993811972563501}
  - component: {fileID: 5922124948139013012}
  - component: {fileID: 3020242482656065494}
  m_Layer: 5
  m_Name: icon
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &7043993811972563501
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1669593903101958263}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5711007784856473227}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: -27.1, y: 0}
  m_SizeDelta: {x: 42, y: 43}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &5922124948139013012
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1669593903101958263}
  m_CullTransparentMesh: 1
--- !u!114 &3020242482656065494
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1669593903101958263}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: bc7311ca41ae4d34aa7bef75a206791d, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!1 &1899946023886520621
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1443900031349891642}
  - component: {fileID: 7314649267119051185}
  m_Layer: 5
  m_Name: GetChestExp
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1443900031349891642
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899946023886520621}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7508783757578546784}
  m_Father: {fileID: 429116095948613835}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 1000.08777, y: 999.6435}
  m_SizeDelta: {x: 120, y: 132}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &7314649267119051185
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899946023886520621}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c6a1874b3fa04eff90c2f4bbf5ed0f21, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_TargetUI: {fileID: 1443900031349891642}
  m_WorldOffset: {x: 0, y: 0, z: 0}
  m_UIOffset: {x: 0, y: 0, z: 0}
  m_UpdateType: 0
  m_FollowPosition: {x: 0, y: 0, z: 0}
  m_Root: {fileID: 7508783757578546784}
  m_TxtNum: {fileID: 9095737149107458089}
--- !u!1 &3421671677005260015
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3448465969572109480}
  - component: {fileID: 2700902842755286410}
  - component: {fileID: 2086012353420783233}
  - component: {fileID: 2894009226788431056}
  - component: {fileID: 4379372372423459202}
  m_Layer: 5
  m_Name: text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &3448465969572109480
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3421671677005260015}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5711007784856473227}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 66, y: 0}
  m_SizeDelta: {x: 138, y: 60}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &2700902842755286410
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3421671677005260015}
  m_CullTransparentMesh: 1
--- !u!114 &2086012353420783233
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3421671677005260015}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f7201a12d95ffc409449d95f23cf332, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 12800000, guid: face6506aacc38d4b8694ecaf79f258c, type: 3}
    m_FontSize: 26
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 2
    m_MaxSize: 28
    m_Alignment: 3
    m_AlignByGeometry: 0
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: +123
--- !u!114 &2894009226788431056
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3421671677005260015}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e19747de3f5aca642ab2be37e372fb86, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_EffectColor: {r: 0.078431375, g: 0.23529413, b: 0.6862745, a: 1}
  m_EffectDistance: {x: 1.5, y: -1.5}
  m_UseGraphicAlpha: 1
--- !u!114 &4379372372423459202
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3421671677005260015}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 45facfdc6a639f041b007c036dc527b8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _gradientType: 1
  _blendMode: 2
  _modifyVertices: 0
  _offset: -0.07
  _zoom: 1
  _effectGradient:
    serializedVersion: 2
    key0: {r: 0.20392159, g: 0.5921569, b: 1, a: 1}
    key1: {r: 0.5921569, g: 0.85098046, b: 1, a: 1}
    key2: {r: 0.5921569, g: 0.85098046, b: 1, a: 0}
    key3: {r: 0.5921569, g: 0.85098046, b: 1, a: 0}
    key4: {r: 0, g: 0, b: 0, a: 0}
    key5: {r: 0, g: 0, b: 0, a: 0}
    key6: {r: 0, g: 0, b: 0, a: 0}
    key7: {r: 0, g: 0, b: 0, a: 0}
    ctime0: 0
    ctime1: 65535
    ctime2: 65535
    ctime3: 65535
    ctime4: 0
    ctime5: 0
    ctime6: 0
    ctime7: 0
    atime0: 0
    atime1: 65535
    atime2: 0
    atime3: 0
    atime4: 0
    atime5: 0
    atime6: 0
    atime7: 0
    m_Mode: 0
    m_ColorSpace: -1
    m_NumColorKeys: 2
    m_NumAlphaKeys: 2
--- !u!1 &4513347109039014699
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 429116095948613835}
  - component: {fileID: 4359680707980283062}
  - component: {fileID: 3082772064624818069}
  - component: {fileID: 3572122437642761000}
  - component: {fileID: 895414031943693905}
  - component: {fileID: 3980068662580207465}
  m_Layer: 5
  m_Name: WorldTopUIPanel
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &429116095948613835
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4513347109039014699}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5254510557639303520}
  - {fileID: 1903267906605356960}
  - {fileID: 9080156823510314842}
  - {fileID: 7488325210841966273}
  - {fileID: 1443900031349891642}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!223 &4359680707980283062
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4513347109039014699}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 2
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 1
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_VertexColorAlwaysGammaSpace: 0
  m_AdditionalShaderChannelsFlag: 25
  m_UpdateRectTransformForStandalone: 0
  m_SortingLayerID: 0
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!114 &3082772064624818069
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4513347109039014699}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 3932215
--- !u!225 &3572122437642761000
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4513347109039014699}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!114 &895414031943693905
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4513347109039014699}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: abb5c4702c7d6e344b9832a97ae1264d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  elementCategory: ~Custom Name~
  elementName: WorldTopUIPanel
  LANDSCAPE: 1
  PORTRAIT: 1
  startHidden: 0
  animateAtStart: 0
  disableWhenHidden: 0
  dontDisableCanvasWhenHidden: 0
  disableGraphicRaycaster: 0
  autoHide: 0
  autoHideDelay: 3
  useCustomStartAnchoredPosition: 0
  customStartAnchoredPosition: {x: 0, y: 0, z: 0}
  executeLayoutFix: 0
  deselectAnySelectedButtonOnShow: 0
  deselectAnySelectedButtonOnHide: 0
  enableAutoSelectButtonAfterShow: 1
  selectedButton: {fileID: 0}
  linkedToNotification: 0
  autoRegister: 1
  isVisible: 1
  inAnimations:
    animationType: 0
    move:
      enabled: 0
      animationType: 0
      moveDirection: 0
      customPosition: {x: 0, y: 0, z: 0}
      easeType: 0
      ease: 1
      animationCurve:
        serializedVersion: 2
        m_Curve: []
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      startDelay: 0
      duration: 0.5
    rotate:
      enabled: 0
      animationType: 0
      rotation: {x: 0, y: 0, z: 0}
      rotateMode: 1
      easeType: 0
      ease: 1
      animationCurve:
        serializedVersion: 2
        m_Curve: []
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      startDelay: 0
      duration: 0.5
    scale:
      enabled: 0
      animationType: 0
      scale: {x: 0, y: 0, z: 0}
      easeType: 0
      ease: 1
      animationCurve:
        serializedVersion: 2
        m_Curve: []
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      startDelay: 0
      duration: 0.5
    fade:
      enabled: 0
      animationType: 0
      alpha: 1
      easeType: 0
      ease: 1
      animationCurve:
        serializedVersion: 2
        m_Curve: []
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      startDelay: 0
      duration: 0.5
    moveIsExpanded:
      m_Target: 0
      speed: 2
      m_Value: 0
    rotateIsExpanded:
      m_Target: 0
      speed: 2
      m_Value: 0
    scaleIsExpanded:
      m_Target: 0
      speed: 2
      m_Value: 0
    fadeIsExpanded:
      m_Target: 0
      speed: 2
      m_Value: 0
  OnInAnimationsStart:
    m_PersistentCalls:
      m_Calls: []
  OnInAnimationsFinish:
    m_PersistentCalls:
      m_Calls: []
  inAnimationsPresetCategoryName: Uncategorized
  inAnimationsPresetName: DefaultPreset
  loadInAnimationsPresetAtRuntime: 0
  inAnimationsSoundAtStart: ~No Sound~
  customInAnimationsSoundAtStart: 0
  inAnimationsSoundAtFinish: ~No Sound~
  customInAnimationsSoundAtFinish: 0
  outAnimations:
    animationType: 1
    move:
      enabled: 0
      animationType: 1
      moveDirection: 0
      customPosition: {x: 0, y: 0, z: 0}
      easeType: 0
      ease: 1
      animationCurve:
        serializedVersion: 2
        m_Curve: []
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      startDelay: 0
      duration: 0.5
    rotate:
      enabled: 0
      animationType: 1
      rotation: {x: 0, y: 0, z: 0}
      rotateMode: 1
      easeType: 0
      ease: 1
      animationCurve:
        serializedVersion: 2
        m_Curve: []
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      startDelay: 0
      duration: 0.5
    scale:
      enabled: 0
      animationType: 1
      scale: {x: 0, y: 0, z: 0}
      easeType: 0
      ease: 1
      animationCurve:
        serializedVersion: 2
        m_Curve: []
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      startDelay: 0
      duration: 0.5
    fade:
      enabled: 0
      animationType: 1
      alpha: 1
      easeType: 0
      ease: 1
      animationCurve:
        serializedVersion: 2
        m_Curve: []
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      startDelay: 0
      duration: 0.5
    moveIsExpanded:
      m_Target: 0
      speed: 2
      m_Value: 0
    rotateIsExpanded:
      m_Target: 0
      speed: 2
      m_Value: 0
    scaleIsExpanded:
      m_Target: 0
      speed: 2
      m_Value: 0
    fadeIsExpanded:
      m_Target: 0
      speed: 2
      m_Value: 0
  OnOutAnimationsStart:
    m_PersistentCalls:
      m_Calls: []
  OnOutAnimationsFinish:
    m_PersistentCalls:
      m_Calls: []
  outAnimationsPresetCategoryName: Uncategorized
  outAnimationsPresetName: DefaultPreset
  loadOutAnimationsPresetAtRuntime: 0
  outAnimationsSoundAtStart: ~No Sound~
  customOutAnimationsSoundAtStart: 0
  outAnimationsSoundAtFinish: ~No Sound~
  customOutAnimationsSoundAtFinish: 0
  loopAnimations:
    autoStart: 0
    move:
      enabled: 0
      movement: {x: 0, y: 0, z: 0}
      easeType: 0
      ease: 1
      animationCurve:
        serializedVersion: 2
        m_Curve: []
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      loops: -1
      loopType: 1
      startDelay: 0
      duration: 0.5
    rotate:
      enabled: 0
      rotation: {x: 0, y: 0, z: 0}
      easeType: 0
      ease: 1
      animationCurve:
        serializedVersion: 2
        m_Curve: []
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      loops: -1
      loopType: 1
      rotateMode: 0
      startDelay: 0
      duration: 0.5
    scale:
      enabled: 0
      min: {x: 1, y: 1, z: 1}
      max: {x: 1.05, y: 1.05, z: 1.05}
      easeType: 0
      ease: 1
      animationCurve:
        serializedVersion: 2
        m_Curve: []
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      loops: -1
      loopType: 1
      startDelay: 0
      duration: 0.5
    fade:
      enabled: 0
      min: 0
      max: 1
      easeType: 0
      ease: 1
      animationCurve:
        serializedVersion: 2
        m_Curve: []
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      loops: -1
      loopType: 1
      startDelay: 0
      duration: 0.5
    moveIsExpanded:
      m_Target: 0
      speed: 2
      m_Value: 0
    rotateIsExpanded:
      m_Target: 0
      speed: 2
      m_Value: 0
    scaleIsExpanded:
      m_Target: 0
      speed: 2
      m_Value: 0
    fadeIsExpanded:
      m_Target: 0
      speed: 2
      m_Value: 0
  loopAnimationsPresetCategoryName: Uncategorized
  loopAnimationsPresetName: DefaultPreset
  loadLoopAnimationsPresetAtRuntime: 0
--- !u!114 &3980068662580207465
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4513347109039014699}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a1974ce1a9bf427cbc208f8cd24d4e03, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_ParentPage: {fileID: 0}
  m_SpritesData: []
  m_PanelEventLogType: 0
  _fields: []
  m_HideMask: 0
  m_CustomVisibleFlag: 1
  m_SortingOrder: -1
  m_ImgCoin: {fileID: 8617002499243277293}
  m_ImgGem: {fileID: 3629989064751808041}
  m_ImgTickets: {fileID: 5008812211552818942}
  m_BattleCoinTip: {fileID: 4434448277812113034}
  m_BattleExpTip: {fileID: 7314649267119051185}
--- !u!1 &4822909917196057165
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9080156823510314842}
  - component: {fileID: 264591222477322746}
  - component: {fileID: 5008812211552818942}
  m_Layer: 5
  m_Name: imgTicket
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &9080156823510314842
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4822909917196057165}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1.1, y: 1.1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 429116095948613835}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 3000, y: -3000}
  m_SizeDelta: {x: 40, y: 43}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &264591222477322746
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4822909917196057165}
  m_CullTransparentMesh: 1
--- !u!114 &5008812211552818942
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4822909917196057165}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: 7520bbde31bf9554e8e24c2782d4e9ce, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!1 &5067645796500388064
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5711007784856473227}
  m_Layer: 5
  m_Name: root
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &5711007784856473227
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5067645796500388064}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7043993811972563501}
  - {fileID: 3448465969572109480}
  m_Father: {fileID: 7488325210841966273}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 100, y: 100}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &5210277090961854552
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7488325210841966273}
  - component: {fileID: 4434448277812113034}
  m_Layer: 5
  m_Name: GetChestCoin
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &7488325210841966273
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5210277090961854552}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5711007784856473227}
  m_Father: {fileID: 429116095948613835}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 1000.08777, y: 999.6435}
  m_SizeDelta: {x: 120, y: 132}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &4434448277812113034
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5210277090961854552}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c6a1874b3fa04eff90c2f4bbf5ed0f21, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_TargetUI: {fileID: 7488325210841966273}
  m_WorldOffset: {x: 0, y: 0, z: 0}
  m_UIOffset: {x: 0, y: 0, z: 0}
  m_UpdateType: 0
  m_FollowPosition: {x: 0, y: 0, z: 0}
  m_Root: {fileID: 5711007784856473227}
  m_TxtNum: {fileID: 2086012353420783233}
--- !u!1 &6618598365915143135
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5254510557639303520}
  - component: {fileID: 3210374414852204648}
  - component: {fileID: 8617002499243277293}
  m_Layer: 5
  m_Name: imgCoin
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &5254510557639303520
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6618598365915143135}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1.1, y: 1.1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 429116095948613835}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 3000, y: -3000}
  m_SizeDelta: {x: 41, y: 47}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &3210374414852204648
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6618598365915143135}
  m_CullTransparentMesh: 1
--- !u!114 &8617002499243277293
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6618598365915143135}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: 55ecf48f43af23f4dabfe4392950aeec, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
