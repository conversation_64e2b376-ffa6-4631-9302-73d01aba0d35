using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Qarth;
using Lean.Pool;
using GameWish.Game;
using ScriptBoy.DiggableTerrains2D;

public enum DigEvt
{
	OnDigLevelChange = 99999,
}

public class DigGameMgr : TMonoSingleton<DigGameMgr>
{
	private enum PointState
	{
		die,                        //已经废弃
		wait,                       //等待检测
		collid                      //正在使用
	}

	private class PointData
	{
		public Vector2 pos;               //点位置
		public PointState status;          //点状态
		public GameObject col;          //点实例
	}

	public Camera mainCam;
	public DiggerHead diggerHead;
	public List<Terrain2D> terrain;
	public Transform effectRoot;
	public Transform floorRoot;
	public int curMineCount = 0;
	private float m_CurDigArea = 0;
	public float curDigArea
	{
		get
		{
			return m_CurDigArea;
		}
		set
		{
			m_CurDigArea = value;
			//计算面积
			if ((int)(m_CurDigArea / m_FloorArea) + m_StartLevel > curMaxLevel)
			{
				curMaxLevel = (int)(m_CurDigArea / m_FloorArea) + m_StartLevel;
				EventSystem.S.Send(DigEvt.OnDigLevelChange, curMaxLevel);
			}
		}
	}
	public int curMaxLevel = 1;
	private int m_StartLevel = 1;
	private float m_FloorArea = 196;

	private DigMine m_MinePreSmall;
	private DigMine m_MinePreBig;

	[SerializeField] private List<DigLevel> m_DigLevels;


	private void Start()
	{
		if (mainCam == null)
			mainCam = GameCamMgr.S.mainCam;
		mainCam.orthographic = true;
	}

	public void StartGame()
	{
		StartCoroutine(LoadLevel(new List<int>() { 1, 2, 3 }));
		diggerHead.SetData(TDDigUpgradeInfoTable.GetData(1));
	}

	public void QuitGame()
	{
		for (int i = 0; i < m_DigLevels.Count; i++)
		{
			m_DigLevels[i].Close();
		}
	}


	private IEnumerator LoadLevel(List<int> level)
	{
		m_StartLevel = level[0];
		curMineCount = 0;
		int loadPrefabCount = 0;
		if (m_MinePreSmall == null)
		{
			AddressableResMgr.S.LoadAssetAsyncByName<GameObject>("MineSmall", (obj, state) =>
			{
				if (state)
				{
					m_MinePreSmall = obj.GetComponent<DigMine>();
					loadPrefabCount++;
				}
			});
		}
		else
		{
			loadPrefabCount++;
		}
		if (m_MinePreBig == null)
		{
			AddressableResMgr.S.LoadAssetAsyncByName<GameObject>("MineBig", (obj, state) =>
			{
				if (state)
				{
					m_MinePreBig = obj.GetComponent<DigMine>();
					loadPrefabCount++;
				}
			});
		}
		else
		{
			loadPrefabCount++;
		}
		yield return new WaitUntil(() => loadPrefabCount == 2);
		AddressableResMgr.S.InstantiateAsync("Pre_Floor", (obj, state) =>
		{
			if (state)
			{
				obj.transform.SetParent(floorRoot);
				obj.ResetTrs();
				loadPrefabCount++;
			}
		});


		AddressableResMgr.S.InstantiateAsync("Pre_Floor", (obj, state) =>
		{
			if (state)
			{
				obj.transform.SetParent(floorRoot);
				obj.ResetTrs();
				obj.transform.SetLocalY(-60);
				loadPrefabCount++;
			}
		});
		yield return new WaitUntil(() => loadPrefabCount == 4);

		for (int i = 0; i < level.Count; i++)
		{
			//读表生成地图
			TDDigLevelInfo levelInfo = TDDigLevelInfoTable.GetData(level[i]);
			//生成超级矿石 水晶
			for (int j = 0; j < levelInfo.superMineCount; j++)
			{
				CreateSuperMine(i, j, levelInfo.superMineValue);
			}
			for (int j = 0; j < levelInfo.crystalCount; j++)
			{
				CreateCrystal(i, j + levelInfo.superMineCount - 1, levelInfo.crystalValue);
			}
			//能力值赋值
			m_DigLevels[i].Init(levelInfo);
		}


	}

	private void CreateSuperMine(int index, int parent, int value)
	{
		var pos = m_DigLevels[index].lstMinePos[parent].position;
		var mine = LeanPool.Spawn(m_MinePreBig);
		mine.transform.ResetTrans();
		mine.transform.position = pos;
		mine.price = value;
	}

	private void CreateCrystal(int index, int parent, int value)
	{
		var pos = m_DigLevels[index].lstMinePos[parent].position;
		var mine = LeanPool.Spawn(m_MinePreBig);
		mine.transform.ResetTrans();
		mine.transform.position = pos;
		mine.price = value;
	}

}
