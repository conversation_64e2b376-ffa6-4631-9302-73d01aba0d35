using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Qarth;
using Lean.Pool;
using GameWish.Game;
public class DigGameMgr : TMonoSingleton<DigGameMgr>
{
	private enum PointState
	{
		die,                        //已经废弃
		wait,                       //等待检测
		collid                      //正在使用
	}

	private class PointData
	{
		public Vector2 pos;               //点位置
		public PointState status;          //点状态
		public GameObject col;          //点实例
	}

	public List<DigLand> target;
	public Camera mainCam;
	public DiggerHead diggerHead;
	public GameObject preCollider;

	private void Start()
	{
		mainCam = GameCamMgr.S.mainCam;
		mainCam.orthographic = true;
		AddressableResMgr.S.LoadAssetAsyncByName<GameObject>("MousePoint3", (obj, state) =>
		{
			if (state)
			{
				preCollider = obj;
				//加载资源完成，生成碰撞体
				for (int i = 0; i < target.Count; i++)
				{
					target[i].CreatePoint();
				}
			}
		});

	}

	private float m_LandDis = -10;
	public bool Dig()
	{
		if (diggerHead == null)
		{
			return false;
		}
		if (diggerHead.state != DiggerState.Dig)
		{
			return false;
		}

		//创建一条射线一摄像机为原点
		Ray ray = mainCam.ScreenPointToRay(mainCam.WorldToScreenPoint(diggerHead.transform.position));
		RaycastHit hit;
		//射线碰撞到游戏地形时
		bool working = false;
		if (Physics.Raycast(ray, out hit))
		{
			if (hit.transform.CompareTag("DigLand"))
			{
				//根据碰撞坐标 找到y轴最近的两个land去处理
				float y = hit.point.y;  //-5->0,1  -11->1,2
				float value = (y / m_LandDis);
				int index = Mathf.RoundToInt(value);
				int index2 = index + 1;
				if (index < 0)
				{
					index = 0;
					index2 = 0;
				}
				else if (index >= target.Count - 1)
				{
					index = target.Count - 1;
					index2 = target.Count - 1;
				}

				if (index != index2)
				{
					if (target[index].Dig(hit.point))
					{
						working = true;
					}
					if (target[index2].Dig(hit.point))
					{
						working = true;
					}
				}
				else
				{
					if (target[index].Dig(hit.point))
					{
						working = true;
					}
				}
			}
		}
		return working;
	}
}
