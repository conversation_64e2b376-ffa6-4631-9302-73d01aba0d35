There are 2 PDF files. Please check the Manual first, then go to the Scripting API. Whether you are coming from version 1 or not, you should check the PDFs because everything has changed.


Here are the main changes in version 2.0.0:
- Improved Performance: In the previous version, the destruction was only based on the polygon clipping algorithm with a poor implementation. However, in the current version, the polygon clipping is heavily optimized. Additionally, a new solution has been added that utilizes the marching squares algorithm and chunking technique for maximum performance.

- Added Shapes: In the previous version, each terrain component had a list of points that formed a polygon. However, in the current version, you can create multiple shapes on a single terrain component. A shape can be defined by a circle, box, polyline, or spline.

- Added Holes: In the previous version, you were only able to dig along the edges of the terrain. However, in the current version, you can also start digging from the middle of the terrain.

- Added Layers: In the previous version, terrains only had a single layer of textures. However, in the current version, you can add a maximum of 4 layers, which will be blended by the Splat Map.

- Improved Edge UV Mapping: In the previous version, the UVs were generated by the CPU. However, in the current version, it is handled by shaders.

- Added Physics Mode: This experimental feature adds Rigidbody components to terrains.

- Shovel: In the previous version, the shovel was a simple circle. However, in the current version, it can be any shape.

- More Scripting API: In the previous version, you only had access to the shovel component with a few functions. However, in the current version, you can directly edit terrains without the shovel component.

- Added Fill Function: In the previous version, you were only able to dig terrain and remove areas. However, in the current version, you can also add areas. This function acts as the reverse version of the dig function.

- Collider Offset: In the current version, you can add an offset to the polygon collider.

- Source Code: Unlike the previous version, the source code is now included. However, please note that the documentation does not cover how it works. It only describes the main classes and functions.

- Not Compatible: It is not compatible with the previous version, so you may need to recreate terrains from scratch.