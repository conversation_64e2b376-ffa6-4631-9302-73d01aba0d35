using UnityEngine;
using System.Collections;
using Lean.Pool;
using DG.Tweening;

public class DiggerPiece : MonoBehaviour
{
	public Rigidbody2D rigidbody;
	public bool move2Head = false;

	public void Move2Head(Vector3 pos)
	{
		if (move2Head) return;
		rigidbody.isKinematic = true;
		move2Head = true;
		transform.DOMove(pos, 0.2f).OnComplete(() =>
		{
			Reset();
			LeanPool.Despawn(this);
		});
	}

	public void Reset()
	{
		move2Head = false;
		rigidbody.isKinematic = false;
	}

}