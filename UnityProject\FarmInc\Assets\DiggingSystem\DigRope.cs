using UnityEngine;
using System.Collections;
using Lean.Pool;
using DG.Tweening;
using GameWish.Game;
using Qarth;


public class DigRope : MonoBehaviour
{
	public SpriteRenderer rope;
	public SpriteRenderer ropeShadow;

	public Transform startRoot;
	public Transform endRoot;

	void Start()
	{
		ReCaculateRope();
	}


	public void ReCaculateRope()
	{
		//计算距离
		var dir = endRoot.position - startRoot.position;
		rope.size = new Vector2(0.33f, dir.magnitude);
		ropeShadow.size = new Vector2(0.33f, dir.magnitude);
		//设置绳子的角度,lerp
		rope.transform.rotation = Quaternion.LookRotation(Vector3.forward, -dir);
	}
}