using UnityEngine;

/// <summary>
/// 测试脚本，用于验证SpriteEraser的立体效果功能
/// </summary>

namespace GameWish.Game
{
	public class SpriteEraserTester : MonoBehaviour
	{
	    [Header("Test Settings")]
	    public SpriteEraser spriteEraser;
	    public KeyCode testKey = KeyCode.Space;
	    public Vector2 testPosition = Vector2.zero;
	    
	    void Update()
	    {
	        if (Input.GetKeyDown(testKey))
	        {
	            TestEraseWithEffect();
	        }
	        
	        // 鼠标点击测试
	        if (Input.GetMouseButtonDown(0))
	        {
	            Vector3 mouseWorldPos = Camera.main.ScreenToWorldPoint(Input.mousePosition);
	            mouseWorldPos.z = 0;
	            TestEraseAtPosition(mouseWorldPos);
	        }
	    }
	    
	    void TestEraseWithEffect()
	    {
	        if (spriteEraser != null)
	        {
	            Debug.Log("Testing erase with 3D effect at position: " + testPosition);
	            spriteEraser.ErasePixels(testPosition, spriteEraser.eraserRadius);
	        }
	        else
	        {
	            Debug.LogWarning("SpriteEraser reference is null!");
	        }
	    }
	    
	    void TestEraseAtPosition(Vector2 position)
	    {
	        if (spriteEraser != null)
	        {
	            Debug.Log("Testing erase with 3D effect at mouse position: " + position);
	            spriteEraser.ErasePixels(position, spriteEraser.eraserRadius);
	        }
	        else
	        {
	            Debug.LogWarning("SpriteEraser reference is null!");
	        }
	    }
	    
	    void OnGUI()
	    {
	        GUILayout.BeginArea(new Rect(10, 10, 300, 200));
	        GUILayout.Label("Sprite Eraser 3D Effect Tester");
	        GUILayout.Label("Press " + testKey + " to test erase at test position");
	        GUILayout.Label("Click mouse to test erase at mouse position");
	        
	        if (spriteEraser != null)
	        {
	            GUILayout.Label("3D Effect Enabled: " + spriteEraser.enable3DEffect);
	            GUILayout.Label("Shadow Width: " + spriteEraser.shadowWidth);
	            GUILayout.Label("Shadow Color: " + spriteEraser.shadowColor);
	        }
	        
	        GUILayout.EndArea();
	    }
	}
	
}