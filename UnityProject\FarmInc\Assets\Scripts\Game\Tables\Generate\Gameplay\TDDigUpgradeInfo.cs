//Auto Generate Don't Edit it
using UnityEngine;
using System;
using System.IO;
using System.Collections;
using System.Collections.Generic;
using Qarth;

namespace GameWish.Game
{
    public partial class TDDigUpgradeInfo
    {
        
       
        private EInt m_Id = 0;   
        private string m_Icon;   
        private EInt m_DiggerPower = 0;   
        private EInt m_DiggerCost = 0;   
        private EInt m_CleanerPower = 0;   
        private EInt m_CleanerCost = 0;  
        
        //private Dictionary<string, TDUniversally.FieldData> m_DataCacheNoGenerate = new Dictionary<string, TDUniversally.FieldData>();
      
        /// <summary>
        /// ID
        /// </summary>
        public  int  id {get { return m_Id; } }
       
        /// <summary>
        /// 图标
        /// </summary>
        public  string  icon {get { return m_Icon; } }
       
        /// <summary>
        /// 锯子能力值
        /// </summary>
        public  int  diggerPower {get { return m_DiggerPower; } }
       
        /// <summary>
        /// 锯子升级消耗
        /// </summary>
        public  int  diggerCost {get { return m_DiggerCost; } }
       
        /// <summary>
        /// 吸头能力值
        /// </summary>
        public  int  cleanerPower {get { return m_CleanerPower; } }
       
        /// <summary>
        /// 吸头升级消耗
        /// </summary>
        public  int  cleanerCost {get { return m_CleanerCost; } }
       

        public void ReadRow(DataStreamReader dataR, int[] filedIndex)
        {
          //var schemeNames = dataR.GetSchemeName();
          int col = 0;
          while(true)
          {
            col = dataR.MoreFieldOnRow();
            if (col == -1)
            {
              break;
            }
            switch (filedIndex[col])
            { 
            
                case 0:
                    m_Id = dataR.ReadInt();
                    break;
                case 1:
                    m_Icon = dataR.ReadString();
                    break;
                case 2:
                    m_DiggerPower = dataR.ReadInt();
                    break;
                case 3:
                    m_DiggerCost = dataR.ReadInt();
                    break;
                case 4:
                    m_CleanerPower = dataR.ReadInt();
                    break;
                case 5:
                    m_CleanerCost = dataR.ReadInt();
                    break;
                default:
                    //TableHelper.CacheNewField(dataR, schemeNames[col], m_DataCacheNoGenerate);
                    break;
            }
          }

        }
        
        public static Dictionary<string, int> GetFieldHeadIndex()
        {
          Dictionary<string, int> ret = new Dictionary<string, int>(6);
          
          ret.Add("Id", 0);
          ret.Add("Icon", 1);
          ret.Add("DiggerPower", 2);
          ret.Add("DiggerCost", 3);
          ret.Add("CleanerPower", 4);
          ret.Add("CleanerCost", 5);
          return ret;
        }
    } 
}//namespace LR