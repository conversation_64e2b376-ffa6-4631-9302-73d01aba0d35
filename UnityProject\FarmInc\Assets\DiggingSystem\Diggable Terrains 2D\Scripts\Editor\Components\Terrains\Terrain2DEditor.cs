using System;
using System.Collections.Generic;
using UnityEditor;
using UnityEditorInternal;
using UnityEngine;
using UnityEngine.Rendering;

namespace ScriptBoy.DiggableTerrains2D
{
    abstract class Terrain2DEditor : Editor
    {
        [InitializeOnLoadMethod]
        static void CheckEnterPlayModeOptions()
        {
            RenderPipelineManager.activeRenderPipelineTypeChanged += OnActiveRenderPipelineTypeChanged;
        }

        static void OnActiveRenderPipelineTypeChanged()
        {
            if (Application.isPlaying) return;

            foreach (var terrain in FindObjectsOfType<Terrain2D>(true))
            {
                terrain.Build();
            }
        }

        static class GUIContents
        {
            public static readonly GUIContent buildOnAwake = new GUIContent("Build On Awake", "Build the terrain when the scene loads?");

            public static readonly GUIContent asset = new GUIContent("Asset", "By default, you can edit the terrain by creating multiple Shape2D components. However, you can also create a Terrain2DAsset to directly edit it.");
            public static readonly GUIContent edge = new GUIContent("Edge", "The edge settings.");
            public static readonly GUIContent edgeHeight = new GUIContent("Height", "The height of edges.");
            public static readonly GUIContent edgeOffset = new GUIContent("Offset", "The offset of edges.");
            public static readonly GUIContent edgeCornerType = new GUIContent("Corner Type", "To handle sharp corners, you have three options:\nSimple: No changes are made to the corners.\nNormal: 2 points are added to the corners.\nRounded: N points are added to make the corners rounded.");
            public static readonly GUIContent edgeUVMapping = new GUIContent("UV Mapping", "Choose X for horizontal texture mapping, Y for vertical mapping, and XY for both axes.");

            public static readonly GUIContent isDiggable = new GUIContent("Is Diggable", "Is the terrain diggable in runtime?");
            public static readonly GUIContent isFillable = new GUIContent("Is Fillable", "Is the terrain fillable in runtime?");

            public static readonly GUIContent simplification = new GUIContent("Simplification", "The simplification threshold.");

            public static readonly GUIContent saveSettings = new GUIContent("Runtime Save Settings", "Customize how terrain data is collected when you use the Save or GetData methods during runtime.");
            public static readonly GUIContent compressionMethod = new GUIContent("Compression Method", "Compression method used when saving data.");
            public static readonly GUIContent compressionLevel = new GUIContent("Compression Level", "Compression level used when saving data.");
            public static readonly GUIContent includeSplatMapInSave = new GUIContent("Include Splat Map", "Whether to include the splat map in the save data.");
            public static readonly GUIContent compressSplatMapInSave = new GUIContent("Compress Splat Map", "Whether to compress the splat map in the save data.");


            public static readonly GUIContent collider = new GUIContent("Collider", "The collider settings.");
            public static readonly GUIContent colliderOffset = new GUIContent("Offset", "The offset of colliders.");

            public static readonly GUIContent layers = new GUIContent("Layers", "You can set the colors and textures of terrains, with a maximum of 4 layers.");

            public static readonly GUIContent splatMap = new GUIContent("Splat Map", "The splat map settings.");
            public static readonly GUIContent splatMapTexture = new GUIContent("Texture", "The texture of splat map.");
            public static readonly GUIContent splatMapUVRect = new GUIContent("UV Rect", "The UV rect of splat map.");
        }


        protected SerializedProperty m_AssetProp;
        SerializedProperty m_BuildOnAwakeProp;

        SerializedProperty m_CompressionMethodProp;
        SerializedProperty m_CompressionLevelProp;
        SerializedProperty m_IncludeSplatMapInSaveProp;
        SerializedProperty m_CompressSplatMapInSaveProp;

        [NonSerialized] int m_SaveDataSize = 0;
        [NonSerialized] float m_SavingDuration = 0;
        [NonSerialized] float m_LoadingDuration = 0;

        SerializedProperty m_IsDiggableProp;
        SerializedProperty m_IsFillableProp;

        SerializedProperty m_SortingLayerIDProp;
        SerializedProperty m_SortingOrderProp;
        SerializedProperty m_SimplificationProp;

        SerializedProperty m_EdgeHeightProp;
        SerializedProperty m_EdgeOffsetProp;
        SerializedProperty m_EdgeCornerTypeProp;
        SerializedProperty m_EdgeUVMappingProp;

        SerializedProperty m_ColliderOffsetProp;

        SerializedProperty m_LayersProp;

        SerializedProperty m_SplatMapTextureProp;
        SerializedProperty m_SplatMapUVRectProp;

        SerializedProperty m_UseDefaultCheckerTextureProp;
        SerializedProperty m_SplatMapEditorIDProp;

        [SerializeField] Texture m_DefaultCheckerTexture;
        protected Terrain2D m_Terrain;
        protected Tool m_ActiveTool;

        static bool s_FoldoutShapes;

        Tool m_MapTool;
        Tool m_SplatMapTool;
        Tool m_SplatMapTransformTool;

        TerrainLayerList m_LayerList;

        protected abstract Type mapDataType { get; }

        protected virtual void OnEnable()
        {
            this.FindProperties();
   
            serializedObject.Update();

            m_Terrain = target as Terrain2D;

            if (m_Terrain.name.StartsWith("GameObject"))
            {
                m_Terrain.name = m_Terrain.GetType().Name.AddWordSpaces();
            }

            m_LayerList = new TerrainLayerList(m_LayersProp);


            m_MapTool = new DirectMapEditor(this);
            m_SplatMapTool = new Tool(() => SplatMapPainter.DoPaint(this), SaveSplatMapToDisk);
            m_SplatMapTransformTool = new Tool(DoSplatMapTransformTool);

            var map = m_SplatMapTextureProp.objectReferenceValue as Texture2D;

            SceneView.duringSceneGui += OnSceneGUI;
            Undo.undoRedoPerformed += UndoRedoPerformed;

            if (m_UseDefaultCheckerTextureProp.boolValue)
            {
                m_UseDefaultCheckerTextureProp.boolValue = false;
                var layer = m_LayersProp.GetArrayElementAtIndex(0);
                layer.FindPropertyRelative("fillTexture").objectReferenceValue = m_DefaultCheckerTexture;
                layer.FindPropertyRelative("edgeTexture").objectReferenceValue = m_DefaultCheckerTexture;
                serializedObject.ApplyModifiedProperties();
            }
        }

        void OnDisable()
        {
            SceneView.duringSceneGui -= OnSceneGUI;

            if (m_ActiveTool != null)
            {
                m_ActiveTool.onDisable?.Invoke();
                Tools.hidden = false;
            }

            Undo.undoRedoPerformed -= UndoRedoPerformed;
        }

        void UndoRedoPerformed()
        {
            if (!Application.isPlaying)
            {
                TerrainTracker.SerDirty(m_Terrain);

            }
        }

        public override void OnInspectorGUI()
        {
            if (target == null) return;

            GUI.enabled = !Application.isPlaying;
            EditorGUI.BeginChangeCheck();

            OnShapesGUI();
            OnAssetGUI();
            EditorGUILayout.Space();
            OnHeaderGUI();
            EditorGUILayout.Space();
            OnEdgeGUI();
            OnColliderGUI();
            OnLayersGUI();
            OnSplatMapGUI();
            OnFooterGUI();
        
            if (EditorGUI.EndChangeCheck())
            {
                if (serializedObject.ApplyModifiedProperties())
                {
                    TerrainTracker.SerDirty(m_Terrain);
                }
            }

            GUI.enabled = true;

            if (Application.isPlaying)
            {
                EditorGUILayout.Space();
                EditorGUILayout.HelpBox("Cannot change terrain settings during runtime.", MessageType.Warning);
                EditorGUILayout.Space();
            }

            EditorGUILayout.Space();

            EditorGUI.BeginChangeCheck();
            DrawSaveOptionsGUI();
            if (EditorGUI.EndChangeCheck())
            {
                serializedObject.ApplyModifiedPropertiesWithoutUndo();
            }

            EditorGUILayout.Space();
            EditorGUILayout.Space();
        }

        void NewAsset()
        {
            string path = EditorUtility.SaveFilePanelInProject("Creating Terrain 2D Asset", target.name, "Asset","", FileUtility.ActiveFolderPath());
            if (path != "")
            {
                AssetDatabase.DeleteAsset(path);
                Terrain2DAsset asset = (Terrain2DAsset)CreateInstance(mapDataType);
                if (!m_Terrain.isBuilt) m_Terrain.Build();
                m_Terrain.WriteAsset(asset);
                AssetDatabase.CreateAsset(asset, path);
                AssetDatabase.Refresh();
                m_AssetProp.objectReferenceValue = asset;
                serializedObject.ApplyModifiedProperties();
                m_Terrain.Build();
            }
        }

        void OnAssetGUI()
        {
            if (m_AssetProp.objectReferenceValue != null)
            {
                DrawToolButton(m_MapTool, "Edit Mode");
                if (m_ActiveTool == m_MapTool)
                {
                    m_MapTool.onGUI?.Invoke();
                }
            }


            using (new GUILayout.HorizontalScope())
            {
                EditorGUILayout.ObjectField(m_AssetProp, mapDataType, GUIContents.asset);

                if (m_AssetProp.objectReferenceValue == null)
                {
                    if (GUILayout.Button("New", GUILayout.Width(50)))
                    {
                        EditorApplication.delayCall += NewAsset;
                    }
                }
                else
                {
                    bool hasShape = m_Terrain.GetComponentInChildren<Shape2D>() != null;
                    Terrain2D terrain = target as Terrain2D;
                    Terrain2DAsset asset = m_AssetProp.objectReferenceValue as Terrain2DAsset;

                    if (GUILayout.Button("Remove", GUILayout.ExpandWidth(false)))
                    {
                        m_AssetProp.objectReferenceValue = null;
                    }

                    if (!asset.isEmpty && GUILayout.Button("Clear", GUILayout.ExpandWidth(false)))
                    {
                        Undo.RegisterCompleteObjectUndo(asset, "Clear Terrain2DAsset");
                        asset.Clear();
                        EditorUtility.SetDirty(asset);
                        terrain.Build();
                    }

                    if (hasShape && GUILayout.Button("Rest", GUILayout.ExpandWidth(false)))
                    {
                        Undo.RegisterCompleteObjectUndo(asset, "Rest Terrain2DAsset");
                        m_AssetProp.objectReferenceValue = null;
                        serializedObject.ApplyModifiedPropertiesWithoutUndo();
                        terrain.Build();
                        terrain.WriteAsset(asset);
                        m_AssetProp.objectReferenceValue = asset;
                        serializedObject.ApplyModifiedPropertiesWithoutUndo();
                        EditorUtility.SetDirty(asset);
                        EditorUtility.SetDirty(target);
                    }
                }
            }
        }

        protected override void OnHeaderGUI()
        {
            SortingLayerUtility.RenderSortingLayerFields(m_SortingOrderProp, m_SortingLayerIDProp);
            EditorGUILayout.Slider(m_SimplificationProp, Terrain2D.MinSimplification, Terrain2D.MaxSimplification, GUIContents.simplification);

            EditorGUILayout.PropertyField(m_BuildOnAwakeProp, GUIContents.buildOnAwake);
            EditorGUILayout.PropertyField(m_IsDiggableProp, GUIContents.isDiggable);
            EditorGUILayout.PropertyField(m_IsFillableProp, GUIContents.isFillable);
        }

        void DrawSaveOptionsGUI()
        {
            bool foldout = m_CompressionMethodProp.isExpanded;
            foldout = EditorGUILayout.Foldout(foldout, GUIContents.saveSettings);
            m_CompressionMethodProp.isExpanded = foldout;
            if (!foldout) return;

            using (new EditorGUI.IndentLevelScope())
            {
                EditorGUILayout.PropertyField(m_CompressionMethodProp, GUIContents.compressionMethod);
                EditorGUILayout.PropertyField(m_CompressionLevelProp, GUIContents.compressionLevel);

                if (m_SplatMapTextureProp.objectReferenceValue != null)
                {
                    EditorGUILayout.PropertyField(m_IncludeSplatMapInSaveProp, GUIContents.includeSplatMapInSave);
                    if (m_IncludeSplatMapInSaveProp.boolValue)
                    {
                        EditorGUILayout.PropertyField(m_CompressSplatMapInSaveProp, GUIContents.compressSplatMapInSave);
                    }
                }

                OnSavingOptionsGUI();

                if (m_Terrain.isActiveAndEnabled)
                {
                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.HelpBox($" Data Size : {m_SaveDataSize:n0} bytes\n Saving Duration: {m_SavingDuration:n0} ms\n Loading Duration: {m_LoadingDuration:n0} ms ", MessageType.Info);

                    if (GUILayout.Button("Test", GUILayout.ExpandHeight(true)))
                    {
                        if (!m_Terrain.isBuilt) m_Terrain.Build();

                        Timer.Start();
                        byte[] data = m_Terrain.GetData();
                        m_SaveDataSize = data.Length;
                        m_SavingDuration = Timer.Stop();

                        Timer.Start();
                        m_Terrain.LoadData(data);
                        m_LoadingDuration = Timer.Stop();
                    }
                    EditorGUILayout.EndHorizontal();
                }
            }
        }

        protected virtual void OnSavingOptionsGUI()
        {

        }

        protected virtual void OnFooterGUI()
        {

        }

        void OnShapesGUI()
        {
            bool hasShape = m_Terrain.GetComponentInChildren<Shape2D>() != null;

            GUILayout.BeginHorizontal();
            if (GUILayout.Button("New Shape", GUILayout.Height(25)))
            {
                NewShape();
            }

            if (hasShape && GUILayout.Button("Delete Shapes", GUILayout.Height(25)))
            {
                DeleteShapes();
            }
            GUILayout.EndHorizontal();
        }

        void NewShape()
        {
            var menu = new GenericMenu();
            var types = AssemblyUtility.FindSubclassOf<Shape2D>();
            foreach (var type in types)
            {
                string name = type.Name.AddWordSpaces();
                menu.AddItem(new GUIContent(type.Name.AddWordSpaces()), false, () =>
                {
                    GameObject gameObject = new GameObject(name.Replace(" Shape 2D", ""), type);
                    gameObject.transform.SetParent(m_Terrain.transform, false);
                    Undo.RegisterCreatedObjectUndo(gameObject, "Create Shape");
                    TerrainTracker.SerDirty(m_Terrain);
                });
            }
            menu.ShowAsContext();
        }

        void DeleteShapes()
        {
            var shapes = m_Terrain.GetComponentsInChildren<Shape2D>();
            foreach (var shape in shapes)
            {
                Undo.DestroyObjectImmediate(shape.gameObject);
            }
            TerrainTracker.SerDirty(m_Terrain);
        }

        void OnLayersGUI()
        {
            serializedObject.forceChildVisibility = true;
            bool foldout = m_LayersProp.isExpanded;
            foldout = EditorGUILayout.Foldout(foldout, GUIContents.layers);
            m_LayersProp.isExpanded = foldout;

            if (foldout)
            {
                EditorGUI.indentLevel++;
                m_LayerList.DoLayoutList();
                EditorGUI.indentLevel--;
            }
            serializedObject.forceChildVisibility = false;
        }

        void OnColliderGUI()
        {
            bool foldout = m_ColliderOffsetProp.isExpanded;
            foldout = EditorGUILayout.Foldout(foldout, GUIContents.collider);
            m_ColliderOffsetProp.isExpanded = foldout;
            if (!foldout) return;

            using (new EditorGUI.IndentLevelScope())
            {
                EditorGUILayout.Slider(m_ColliderOffsetProp, Terrain2D.MinColliderOffset, Terrain2D.MaxColliderOffset, GUIContents.colliderOffset);
            }
        }

        void OnEdgeGUI()
        {
            bool foldout = m_EdgeHeightProp.isExpanded;
            foldout = EditorGUILayout.Foldout(foldout, GUIContents.edge);
            m_EdgeHeightProp.isExpanded = foldout;
            if (foldout)
            {
                EditorGUI.indentLevel++;
                EditorGUILayout.Slider(m_EdgeHeightProp, Terrain2D.MinEdgeHeight, Terrain2D.MaxEdgeHeight, GUIContents.edgeHeight);
                EditorGUILayout.Slider(m_EdgeOffsetProp, Terrain2D.MinEdgeOffset, Terrain2D.MaxEdgeOffset, GUIContents.edgeOffset);
                EditorGUILayout.PropertyField(m_EdgeCornerTypeProp, GUIContents.edgeCornerType);
                EditorGUILayout.PropertyField(m_EdgeUVMappingProp, GUIContents.edgeUVMapping);
                EditorGUI.indentLevel--;
            }
        }

        void OnSplatMapGUI()
        {
            if (m_LayersProp.arraySize <= 1) return;

            bool foldout = m_SplatMapTextureProp.isExpanded;
            foldout = EditorGUILayout.Foldout(foldout, GUIContents.splatMap);
            m_SplatMapTextureProp.isExpanded = foldout;
            if (!foldout) return;


            using (new EditorGUI.IndentLevelScope())
            {
                Texture2D map = m_SplatMapTextureProp.objectReferenceValue as Texture2D;

                if (map != null)
                {
                    if (!map.isReadable)
                    {
                        EditorGUILayout.HelpBox("The texture Read/Write is disabled.", MessageType.Error);
                    }
                    else
                    {
                        EditorGUILayout.BeginHorizontal();
                        DrawToolButton(m_SplatMapTool, "Paint");
                        DrawToolButton(m_SplatMapTransformTool, "Transform");
                        EditorGUILayout.EndHorizontal();

                        if (m_ActiveTool == m_SplatMapTool)
                        {
                            SplatMapPainter.DrawBrushSettingsGUI(this);
                        }

                        EditorGUILayout.Space();
                    }
                }
                else
                {
                    DrawToolButton(m_SplatMapTransformTool, "Transform");
                }


                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.PropertyField(m_SplatMapTextureProp, GUIContents.splatMapTexture);

                Rect rect = GUILayoutUtility.GetLastRect();
                rect = GUIUtility.GUIToScreenRect(rect);

                if (GUILayout.Button("New", GUILayout.Width(40)))
                {
                    SplatMapCreatorWindow.Open(rect, this);
                }
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.PropertyField(m_SplatMapUVRectProp, GUIContents.splatMapUVRect);
            }
        }

        protected void DrawToolButton(Tool tool, string lable)
        {
            bool changed = GUI.changed;
            EditorGUI.BeginChangeCheck();
            bool editMode = m_ActiveTool == tool;
            EditModeButton.Draw(ref editMode, lable);
            if (EditorGUI.EndChangeCheck())
            {
                if (m_ActiveTool != null)
                    m_ActiveTool.onDisable?.Invoke();

                m_ActiveTool = editMode ? tool : null;
            }
            GUI.changed = changed;
        }

        protected virtual void OnSceneGUI(SceneView scene)
        {
            if (target == null) return;

            serializedObject.Update();

            EditorGUI.BeginChangeCheck();

            if (Tools.hidden = m_ActiveTool != null)
            {
                Transform transform = m_Terrain.transform;
                using (new EditorGUI.DisabledGroupScope(true))
                {
                    Handles.DoPositionHandle(transform.position, transform.rotation);
                }
            }

            if (m_ActiveTool != null)
            {
                int controlID = GUIUtility.GetControlID(FocusType.Passive); ;
                HandleUtility.AddDefaultControl(controlID);
                m_ActiveTool.onSceneGUI();
            }

            if (EditorGUI.EndChangeCheck())
            {
                serializedObject.ApplyModifiedProperties();
                TerrainTracker.SerDirty(m_Terrain);
                scene.Repaint();
            }
        }

        bool DoMoveHandle(ref Vector2 po, Quaternion q)
        {
            EditorGUI.BeginChangeCheck();
            float handleSize = HandleUtility.GetHandleSize(po) * 0.1f;
            po = Handles.FreeMoveHandle(po, handleSize, Vector3.zero, Handles.CubeHandleCap);

            if (Event.current.control)
                po = EditorGridUtility.SnapToGrid2D(po);
            return EditorGUI.EndChangeCheck();
        }

        bool DoPositionHandle(ref Vector2 po, Quaternion q)
        {
            EditorGUI.BeginChangeCheck();
            po = Handles.DoPositionHandle(po, q);
            if (Event.current.control)
                po = EditorGridUtility.SnapToGrid2D(po);
            return EditorGUI.EndChangeCheck();
        }

        void SaveSplatMapToDisk()
        {
            Texture2D map = m_SplatMapTextureProp.objectReferenceValue as Texture2D;
            int w = map.width;
            int h = map.height;

            var temp = RenderTexture.GetTemporary(w, h, 0, RenderTextureFormat.ARGB32);
            Graphics.CopyTexture(map, temp);
            Rect r = new Rect(0, 0, w, h);
            RenderTexture.active = temp;
            map.ReadPixels(r, 0, 0);
            map.Apply();
            RenderTexture.active = null;
            RenderTexture.ReleaseTemporary(temp);

            EditorUtility.SetDirty(map);
            AssetDatabase.SaveAssetIfDirty(map);
            AssetDatabase.Refresh();


            string path = AssetDatabase.GetAssetPath(map);
            path = FileUtility.GetAbsolutePath(path);
            var bytes = map.EncodeToPNG();
            System.IO.File.WriteAllBytes(path, bytes);


            path = FileUtil.GetProjectRelativePath(path);
            var importer = AssetImporter.GetAtPath(path) as TextureImporter;


            importer.textureCompression = TextureImporterCompression.Uncompressed;
            importer.maxTextureSize = 2048;
            importer.SaveAndReimport();
            map = AssetDatabase.LoadAssetAtPath<Texture2D>(path);
            m_SplatMapTextureProp.objectReferenceValue = map;
            m_SplatMapTextureProp.serializedObject.ApplyModifiedProperties();

            m_Terrain.Build();
        }

        void SaveSplatMap()
        {
            Texture2D map = m_SplatMapTextureProp.objectReferenceValue as Texture2D;
            int w = map.width;
            int h = map.height;

            var temp = RenderTexture.GetTemporary(w, h, 0, RenderTextureFormat.ARGB32);
            Graphics.CopyTexture(map, temp);
            Rect r = new Rect(0, 0, w, h);
            RenderTexture.active = temp;
            map.ReadPixels(r, 0, 0);
            map.Apply();
            RenderTexture.ReleaseTemporary(temp);
            EditorUtility.SetDirty(map);
            AssetDatabase.SaveAssetIfDirty(map);
            AssetDatabase.Refresh();
        }

        void DoSplatMapTransformTool()
        {
            if (m_LayersProp.arraySize <= 1)
            {
                if (m_ActiveTool == m_SplatMapTransformTool)
                {
                    m_ActiveTool = null;
                }
                return;
            }

            Rect rect = m_SplatMapUVRectProp.rectValue;

            Vector2 position = rect.position;
            Vector2 scale = rect.size;

            if (DoRectHandale(ref position, ref scale))
            {
                rect.position = position;
                rect.size = scale;
                m_SplatMapUVRectProp.rectValue = rect;
            }
        }

        void DrawRect(Rect rect, Matrix4x4 local2World)
        {
            Vector2 offset = rect.min;
            Vector2 scale = rect.size;
            Vector2 downL = offset;
            Vector2 downR = new Vector2(offset.x + scale.x, offset.y);
            Vector2 upR = new Vector2(offset.x + scale.x, offset.y + scale.y);
            Vector2 upL = new Vector2(offset.x, offset.y + scale.y);
            Vector2 center = new Vector2(offset.x + scale.x / 2, offset.y + scale.y / 2);

            downL = local2World.MultiplyPoint(downL);
            downR = local2World.MultiplyPoint(downR);
            upR = local2World.MultiplyPoint(upR);
            upL = local2World.MultiplyPoint(upL);
            center = local2World.MultiplyPoint(center);

            using (new Handles.DrawingScope(new Color32(0, 122, 200, 255)))
            {
                Handles.DrawAAPolyLine(1, downL, downR, upR, upL, downL);
            }
        }

        protected bool DoRectHandale(ref Vector2 position, ref Vector2 scale)
        {
            Matrix4x4 local2World = m_Terrain.transform.localToWorldMatrix;
            DrawRect(new Rect(position, scale), local2World);

            bool changed = false;
            using (new Handles.DrawingScope(new Color32(0, 122, 200, 255)))
            {
                Vector2 p = position;
                p = local2World.MultiplyPoint(p);
                if (DoPositionHandle(ref p, Quaternion.identity))
                {
                    p = local2World.inverse.MultiplyPoint(p);
                    position = p;
                    changed = true;
                }

                p = position + scale;
                p = local2World.MultiplyPoint(p);
                if (DoMoveHandle(ref p, Quaternion.identity))
                {
                    p = local2World.inverse.MultiplyPoint(p);
                    scale = p - position;
                    changed = true;
                }
            }

            return changed;
        }

        protected class Tool
        {
            public Action onSceneGUI;
            public Action onGUI;
            public Action onDisable;

            public Tool() { }

            public Tool(Action onSceneGUI, Action onDisable = null)
            {
                this.onSceneGUI = onSceneGUI;
                this.onDisable = onDisable;
            }
        }

        static class SplatMapPainter
        {
            enum BrushColorSelector
            {
                Channel, Layer
            }

            enum BrushType
            {
                Circle, Texture
            }

            static float s_BrushRadius = 2;
            static float s_BrushSoftness = 1;
            static float s_BrushOpacity = 1;
            static float s_BrushColor = 1;
            static BrushType s_BrushType;
            static BrushColorSelector s_BrushColorSelector;
            static Texture s_BrushTexture;
            static Vector2 s_BrushPosition;

            public static void DoPaint(Terrain2DEditor terrainEditor)
            {
                if (terrainEditor.m_LayersProp.arraySize <= 1)
                {
                    if (terrainEditor.m_ActiveTool == terrainEditor.m_SplatMapTool)
                    {
                        terrainEditor.m_SplatMapTool.onDisable();
                        terrainEditor.m_ActiveTool = null;
                    }
                    return;
                }

                Rect rect = terrainEditor.m_SplatMapUVRectProp.rectValue;

                terrainEditor.DrawRect(rect, terrainEditor.m_Terrain.transform.localToWorldMatrix);

                Event e = Event.current;
                Vector2 m = e.mousePosition;
                m = HandleUtility.GUIPointToWorldRay(m).origin;


                int controlID = GUIUtility.GetControlID(FocusType.Passive); ;
                //HandleUtility.AddDefaultControl(controlID);


                Event EVENT = Event.current;
                bool move = false;

                Vector2 newMousePo = HandleUtility.GUIPointToWorldRay(EVENT.mousePosition).origin;
                if (EVENT.alt)
                {
                    s_BrushRadius = (s_BrushPosition - newMousePo).magnitude;
                }
                else
                {
                    move = Vector2.Distance(s_BrushPosition, newMousePo) > s_BrushRadius * 0.2f;
                    if (GUIUtility.hotControl == controlID)
                    {
                        if (move)
                        {
                            float y = s_BrushPosition.y;
                            s_BrushPosition = newMousePo;
                            if (EVENT.control) s_BrushPosition.y = y;
                        }

                    }
                    else s_BrushPosition = newMousePo;
                }

                if (EVENT.type == EventType.MouseDown && EVENT.button == 0)
                {
                    s_BrushPosition = newMousePo;
                    move = true;
                    GUIUtility.hotControl = controlID;
                    EVENT.Use();
                }

                if (EVENT.type == EventType.MouseUp && EVENT.button == 0)
                {
                    var prop = terrainEditor.m_SplatMapEditorIDProp;
                    Undo.RegisterCompleteObjectUndo(terrainEditor.m_SplatMapTextureProp.objectReferenceValue, "Splat Map");
                    terrainEditor.SaveSplatMap();
                    GUIUtility.hotControl = 0;
                    EVENT.Use();
                }

                bool mouseButton = GUIUtility.hotControl == controlID;

                Color color = mouseButton ? Color.cyan : Color.white;
                Handles.color = color;
                Quaternion q = Quaternion.identity;

                if (s_BrushType == BrushType.Circle)
                {
                    Handles.CircleHandleCap(0, s_BrushPosition, q, s_BrushRadius, EVENT.type);
                    Handles.CircleHandleCap(0, s_BrushPosition, q, s_BrushRadius * (1 - s_BrushSoftness), EVENT.type);

                    Handles.color = color.Fade(0.1f);
                    Vector3[] circle = VectorUtility.ConvertToVector3(PolygonUtility.CreateCircle(s_BrushPosition, 20, s_BrushRadius));
                    Handles.DrawAAConvexPolygon(circle);
                }
                else
                {
                    Vector3[] box = VectorUtility.ConvertToVector3(PolygonUtility.CreateBox(s_BrushPosition, Vector2.one * s_BrushRadius * 2));
                    Handles.DrawLine(box[0], box[1]);
                    Handles.DrawLine(box[1], box[2]);
                    Handles.DrawLine(box[2], box[3]);
                    Handles.DrawLine(box[3], box[0]);
                    Handles.color = color.Fade(0.1f);
                    Handles.DrawAAConvexPolygon(box);
                }


                if (move && mouseButton && (EVENT.isMouse && EVENT.type != EventType.MouseUp || EVENT.type == EventType.Used))
                {
                    if (s_BrushType == BrushType.Texture)
                    {
                        if (s_BrushTexture != null)
                        {
                            terrainEditor.m_Terrain.PaintSplatMap(s_BrushPosition, s_BrushTexture, s_BrushRadius * 2, s_BrushOpacity, s_BrushColor);

                        }
                    }
                    else
                    {
                        terrainEditor.m_Terrain.PaintSplatMap(s_BrushPosition, s_BrushRadius, s_BrushSoftness, s_BrushOpacity, s_BrushColor);
                    }
                }
                SceneView.RepaintAll();
            }

            public static void DrawBrushSettingsGUI(Terrain2DEditor terrain2DEditor)
            {
                GUILayout.BeginVertical("Brush Settings", GUI.skin.window);
                s_BrushType = (BrushType)EditorGUILayout.EnumPopup("Paint By", s_BrushType);

                if (s_BrushType == BrushType.Texture)
                {
                    s_BrushTexture = (Texture)EditorGUILayout.ObjectField("Texture", s_BrushTexture, typeof(Texture), false);
                    s_BrushRadius = EditorGUILayout.FloatField("Size", s_BrushRadius * 2) / 2;

                }
                else
                {
                    s_BrushRadius = EditorGUILayout.FloatField("Radius", s_BrushRadius);
                    s_BrushSoftness = EditorGUILayout.Slider("Softness", s_BrushSoftness, 0, 1);
                }


                s_BrushColorSelector = (BrushColorSelector)EditorGUILayout.EnumPopup("Color By", s_BrushColorSelector);

                int n = terrain2DEditor.m_LayersProp.arraySize;
                if (s_BrushColorSelector == BrushColorSelector.Channel)
                {
                    string[] options = new string[n];

                    if (0 < n) options[0] = "Red (Layer 0)";
                    if (1 < n) options[1] = "Green (Layer 1)";
                    if (2 < n) options[2] = "Blue (Layer 2)";
                    if (3 < n) options[3] = "Alpha (Layer 3)";

                    s_BrushColor = EditorGUILayout.Popup("Channel", (int)s_BrushColor, options);
                }
                else
                {
                    s_BrushColor = EditorGUILayout.Slider("Layer", s_BrushColor, 0, n - 1);
                }

                s_BrushOpacity = EditorGUILayout.Slider("Opacity", s_BrushOpacity, 0, 1);
                GUILayout.EndVertical();


                HelpBox.Draw("To resize the brush, hold the <b>Alt</b> button.", 0);

            }
        }

        class SplatMapCreatorWindow : EditorWindow
        {
            Terrain2DEditor m_TerrainEditor;

            enum Res
            {
                _32 = 32,
                _64 = 64,
                _128 = 128,
                _256 = 256,
                _512 = 512,
                _1024 = 1024,
            }

            Res m_Width = Res._256;
            Res m_Height = Res._256;


            public static void Open(Rect rect, Terrain2DEditor terrainEditor)
            {
                EditorApplication.delayCall += () =>
                {
                    var win = GetWindow<SplatMapCreatorWindow>();
                    win.m_TerrainEditor = terrainEditor;
                    win.position = rect;
                    win.maxSize = win.minSize = new Vector2(Mathf.Max(rect.width, 200), 60);
                    win.ShowModal();
                };
            }

            void OnEnable()
            {
                titleContent = new GUIContent("New Splat Map");
            }

            void OnGUI()
            {
                if (m_TerrainEditor == null) Close();

                m_Width = (Res)EditorGUILayout.EnumPopup("Width", m_Width);
                m_Height = (Res)EditorGUILayout.EnumPopup("Height", m_Height);

                GUILayout.BeginHorizontal();
                if (GUILayout.Button("Create")) Create();
                if (GUILayout.Button("Cancel")) Close();
                GUILayout.EndHorizontal();
            }

            void Create()
            {
                Close();
       
                Texture2D map = m_TerrainEditor.m_SplatMapTextureProp.objectReferenceValue as Texture2D;
                string path = EditorUtility.SaveFilePanel("Create Splat Map", FileUtility.ActiveFolderPath(), "Splat Map", "png");
                int width = (int)m_Width;
                int height = (int)m_Height;
                map = new Texture2D(width, height, UnityEngine.Experimental.Rendering.GraphicsFormat.R8G8B8A8_UNorm,0, UnityEngine.Experimental.Rendering.TextureCreationFlags.None);
                int n = width * height;
                Color32[] colors = new Color32[n];
                for (int i = 0; i < n; i++)
                {
                    colors[i] = new Color32(255, 0, 0, 0);
                }
                map.SetPixels32(colors);
                map.Apply();
                System.IO.File.WriteAllBytes(path, map.EncodeToPNG());
                path = FileUtil.GetProjectRelativePath(path);
                AssetDatabase.ImportAsset(path);
                AssetDatabase.Refresh();
                map = AssetDatabase.LoadAssetAtPath<Texture2D>(path);
                m_TerrainEditor.m_SplatMapTextureProp.objectReferenceValue = map;
                TerrainTracker.SerDirty(m_TerrainEditor.m_Terrain);

                var importer = AssetImporter.GetAtPath(path) as TextureImporter;
                importer.textureType = TextureImporterType.Default;
                importer.textureCompression = TextureImporterCompression.Uncompressed;
                importer.maxTextureSize = 2048;
                importer.isReadable = true;
                importer.mipmapEnabled = false;
                importer.sRGBTexture = false;
                importer.SaveAndReimport();
                map = AssetDatabase.LoadAssetAtPath<Texture2D>(path);

                m_TerrainEditor.serializedObject.ApplyModifiedProperties();
            }
        }

        class TerrainLayerList : ReorderableList
        {
            public TerrainLayerList(SerializedProperty elements) : base(elements.serializedObject, elements, true, false, true, true)
            {
                elementHeightCallback = ElementHeightCallback;
                drawElementCallback = DrawElementCallback;
                onCanAddCallback = OnCanAddCallback;
                onCanRemoveCallback = OnCanRemoveCallback;
                onAddCallback = OnAddCallback;
            }

            void OnAddCallback(ReorderableList list)
            {
                serializedProperty.arraySize++;
                var layer = serializedProperty.GetArrayElementAtIndex(serializedProperty.arraySize - 1);
            }

            bool OnCanAddCallback(ReorderableList list)
            {
                return count < 4;
            }

            bool OnCanRemoveCallback(ReorderableList list)
            {
                return count > 1;
            }

            void DrawHeaderCallback(Rect rect)
            {
                EditorGUI.LabelField(rect, "Layers");
            }

            float ElementHeightCallback(int index)
            {
                var prop = serializedProperty.GetArrayElementAtIndex(index);
                return EditorGUI.GetPropertyHeight(prop, true);
            }

            void DrawElementCallback(Rect rect, int index, bool isActive, bool isFocused)
            {
                var prop = serializedProperty.GetArrayElementAtIndex(index);

                EditorGUI.indentLevel++;
                EditorGUI.PropertyField(rect, prop, new GUIContent("Layer " + index), true);
                EditorGUI.indentLevel--;
            }
        }

        static class TerrainTracker
        {
            static List<Terrain2D> s_DirtyTerrains;
            static Dictionary<int, float> s_BuildTimes;

            [InitializeOnLoadMethod]
            static void InitEditor()
            {
                s_DirtyTerrains = new List<Terrain2D>();
                s_BuildTimes = new Dictionary<int, float>();
                EditorApplication.update += Update;
                ShapeTracker.onChanged += OnShapeChanged;
            }

            static void Update()
            {         
                if (EditorApplication.isPlayingOrWillChangePlaymode) return;

                bool delayHeavyTerrains = GUIUtility.hotControl != 0;

                foreach (var terrain in s_DirtyTerrains.ToArray())
                {
                    if (terrain == null)
                    {
                        s_DirtyTerrains.RemoveAll((t) => t == null);
                        return;
                    }
                    bool isHeavyTerrain = IsHeavyTerrain(terrain);
                    if (isHeavyTerrain && delayHeavyTerrains) continue;
                    Timer.Start();
                    terrain.Build();
                    UpdateBuildTime(terrain, Timer.Stop());
                    s_DirtyTerrains.Remove(terrain);
                    EditorUtility.SetDirty(terrain);
                }
            }

            static void OnShapeChanged(Shape2D shape)
            {
                Transform transform = shape.transform;
                foreach (var terrain in Terrain2D.instances)
                {
                    
                    if (transform.IsChildOf(terrain.transform))
                    {
                        SerDirty(terrain);
                    }
                }
            }

            static bool IsHeavyTerrain(Terrain2D terrain)
            {
                int id = terrain.GetInstanceID();
                if (s_BuildTimes.ContainsKey(id))
                {
                    return s_BuildTimes[id] > 40;
                }
                s_BuildTimes.Add(id, 0);
                return false;
            }


            static void UpdateBuildTime(Terrain2D terrain, float time)
            {
                int id = terrain.GetInstanceID();
                if (s_BuildTimes.TryGetValue(id, out float currentTime))
                {
                    s_BuildTimes[id] = (currentTime + time) / 2;
                }
                else
                {
                    s_BuildTimes.Add(id, time);
                }
            }

            public static void SerDirty(Terrain2D terrain)
            {
                if (s_DirtyTerrains.Contains(terrain)) return;

                s_DirtyTerrains.Add(terrain);
            }
        }

        static class Timer
        {
            private static float startTime;

            public static void Start()
            {
                startTime = Time.realtimeSinceStartup;
            }

            public static float Stop()
            {
                return (Time.realtimeSinceStartup - startTime) * 1000f;
            }
        }

        class DirectMapEditor : Tool
        {
            Terrain2DEditor m_Terrain2DEditor;

            static bool s_EditMode;
            static ToolType s_ToolType;
            static float s_Radius = 1;
            static float s_SmoothTime= 0.25f;
            static bool s_Fill;
            static bool s_Apply;
            static Vector2 s_PrevMousePosition;
            static Vector2 s_MouseVelocity;

            enum ToolType
            {
                Circle, Line, Box
            }

            public DirectMapEditor(Terrain2DEditor terrain2DEditor)
            {
                m_Terrain2DEditor = terrain2DEditor;
                onSceneGUI += OnSceneGUI;
                onGUI += OnGUI;
            }

            void OnGUI()
            {
                HelpBox.Draw("Left Click: Fill Terrain\n" +
                    "Right Click: Dig Terrain\n" +
                    "Alt: Change Radius", 0);

                GUILayout.BeginVertical(new GUIContent("Tool Settings"), GUI.skin.window);
                s_ToolType = (ToolType)EditorGUILayout.EnumPopup(s_ToolType);
                s_Radius = EditorGUILayout.FloatField("Radius", s_Radius);
                s_Radius = Mathf.Max(s_Radius, 0.1f);

                if (s_ToolType == ToolType.Circle)
                {
                    s_SmoothTime = EditorGUILayout.FloatField("SmoothTime", s_SmoothTime);
                    s_SmoothTime = Mathf.Max(s_SmoothTime, 0.1f);
                }

                GUILayout.EndVertical();
            }

            void OnSceneGUI()
            {
                Terrain2DAsset asset = (Terrain2DAsset)m_Terrain2DEditor.m_AssetProp.objectReferenceValue;
                if (asset == null) return;

                switch (s_ToolType)
                {
                    case ToolType.Circle: DoCircleTool();
                        break;
                    case ToolType.Line: DoLineTool();
                        break;
                    case ToolType.Box: DoBoxTool();
                        break;
                }
            }

            void DoCircleTool()
            {
                int controlID = GUIUtility.GetControlID(FocusType.Passive); ;
                Event currentEvent = Event.current;
                Vector2 mousePosition = HandleUtility.GUIPointToWorldRay(currentEvent.mousePosition).origin;

                bool paint = false;

                if (GUIUtility.hotControl == controlID)
                {
                    if (currentEvent.type == EventType.MouseUp && (s_Fill && currentEvent.button == 0 || !s_Fill && currentEvent.button == 1))
                    {
                        GUIUtility.hotControl = 0;
                        currentEvent.Use();
                        if (s_Apply) Apply();
                    }
                    else
                    {
                        paint = s_PrevMousePosition != mousePosition;
                    }
                }
                else
                {
                    if (currentEvent.type == EventType.MouseDown && (currentEvent.button == 0 || currentEvent.button == 1))
                    {
                        s_Fill = currentEvent.button == 0;
                        paint = true;
                        GUIUtility.hotControl = controlID;
                        currentEvent.Use();
                    }

                    s_MouseVelocity = Vector2.zero;
                    if(!currentEvent.alt) s_PrevMousePosition = mousePosition;
                }


                Color color = GUIUtility.hotControl == controlID ? Color.cyan : Color.white;
                Handles.color = color.Fade(0.1f);


                Vector3 a = s_PrevMousePosition;
                Vector3 b = mousePosition;

                Vector2[] circle = CreateCircle(a);

                Handles.DrawAAConvexPolygon(VectorUtility.ConvertToVector3(circle));

                if (currentEvent.alt)
                {
                    b = a;
                    s_Radius = Vector2.Distance(s_PrevMousePosition, mousePosition);

                    SceneView.RepaintAll();
                }
                else
                {
                    s_PrevMousePosition = Vector2.SmoothDamp(a, b, ref s_MouseVelocity, s_SmoothTime);
                    b = s_PrevMousePosition;
                }

                Handles.DrawLine(b, mousePosition);

                if (paint)
                {
                    using (new EditScope(m_Terrain2DEditor))
                    {
                        Terrain2D terrain = m_Terrain2DEditor.target as Terrain2D;

                        if (terrain is PolygonTerrain2D)
                        {
                            s_Apply |= terrain.EditByPolygon(circle, s_Fill) > 0;
                        }
                        else
                        {
                            VoxelTerrain2D voxelTerrain = terrain as VoxelTerrain2D;

                            Vector2[] line = PolylineUtility.CreatePolygon(new Vector2[] { a, b }, s_Radius, 0);

                            s_Apply |= voxelTerrain.EditByPolygon(line, s_Fill) > 0;
                            s_Apply |= voxelTerrain.EditByCircle(a, s_Radius, s_Fill) > 0;
                            s_Apply |= voxelTerrain.EditByCircle(b, s_Radius, s_Fill) > 0;
                        }
                    }
                }

                SceneView.RepaintAll();
            }

            void DoLineTool()
            {
                int controlID = GUIUtility.GetControlID(FocusType.Passive); ;
                Event currentEvent = Event.current;
                Vector2 mousePosition = HandleUtility.GUIPointToWorldRay(currentEvent.mousePosition).origin;

                bool paint = false;

                if (GUIUtility.hotControl == controlID)
                {
                    if (currentEvent.type == EventType.MouseUp && (s_Fill && currentEvent.button == 0 || !s_Fill && currentEvent.button == 1))
                    {
                        GUIUtility.hotControl = 0;
                        currentEvent.Use();
                        paint = true;
                    }
                }
                else
                {
                    if (currentEvent.type == EventType.MouseDown && (currentEvent.button == 0 || currentEvent.button == 1))
                    {
                        s_Fill = currentEvent.button == 0;
                        GUIUtility.hotControl = controlID;
                        currentEvent.Use();
                    }

                    s_MouseVelocity = Vector2.zero;
                    if (!currentEvent.alt)
                        s_PrevMousePosition = mousePosition;

                }


                Color color = GUIUtility.hotControl == controlID ? Color.cyan : Color.white;
                Handles.color = color.Fade(0.1f);

                Vector3 a = s_PrevMousePosition;
                Vector3 b = mousePosition;

                Vector2[] circle = CreateCircle(a);
                int roundPointCount = (int)Mathf.Max(circle.Length / 2f - 1, 1);
                Vector2[] polygon = circle;

                if (Vector2.Distance(a, b) > 0 && !currentEvent.alt)
                {
                    polygon = PolylineUtility.CreatePolygon(new Vector2[] { a, b }, s_Radius, roundPointCount);
                }

                Handles.DrawAAConvexPolygon(VectorUtility.ConvertToVector3(polygon));

                if (currentEvent.alt)
                {
                    s_Radius = Vector2.Distance(s_PrevMousePosition, mousePosition);
                }
                else
                {

                }
  
                Handles.DrawLine(b, mousePosition);

                if (paint)
                {
                    using (new EditScope(m_Terrain2DEditor))
                    {
                        Terrain2D terrain = m_Terrain2DEditor.target as Terrain2D;

                        if (terrain is PolygonTerrain2D)
                        {
                            s_Apply |= terrain.EditByPolygon(polygon, s_Fill) > 0;
                        }
                        else
                        {
                            VoxelTerrain2D voxelTerrain = terrain as VoxelTerrain2D;

                            Vector2[] line = PolylineUtility.CreatePolygon(new Vector2[] { a, b }, s_Radius, 0);

                            s_Apply |= voxelTerrain.EditByPolygon(line, s_Fill) > 0;
                            s_Apply |= voxelTerrain.EditByCircle(a, s_Radius, s_Fill) > 0;
                            s_Apply |= voxelTerrain.EditByCircle(b, s_Radius, s_Fill) > 0;
                        }
                    }

                    if (s_Apply) Apply();
                }

                SceneView.RepaintAll();
            }

            void DoBoxTool()
            {
                int controlID = GUIUtility.GetControlID(FocusType.Passive); ;
                Event currentEvent = Event.current;
                Vector2 mousePosition = HandleUtility.GUIPointToWorldRay(currentEvent.mousePosition).origin;

                bool paint = false;

                if (GUIUtility.hotControl == controlID)
                {
                    if (currentEvent.type == EventType.MouseUp && (s_Fill && currentEvent.button == 0 || !s_Fill && currentEvent.button == 1))
                    {
                        GUIUtility.hotControl = 0;
                        currentEvent.Use();
                        paint = true;
                    }
                }
                else
                {
                    if (currentEvent.type == EventType.MouseDown && (currentEvent.button == 0 || currentEvent.button == 1))
                    {
                        s_Fill = currentEvent.button == 0;
                        GUIUtility.hotControl = controlID;
                        currentEvent.Use();
                    }

                    s_MouseVelocity = Vector2.zero;
                    if (!currentEvent.alt)
                        s_PrevMousePosition = mousePosition;

                }





                Color color = GUIUtility.hotControl == controlID ? Color.cyan : Color.white;
                Handles.color = color.Fade(0.1f);
      

                Vector3 a = s_PrevMousePosition;
                Vector3 b = mousePosition;
                Vector3 delta = (b - a);
                if (delta.x == 0) delta.x = 0.01f;
                if (delta.y == 0) delta.y = 0.01f;
                b = a + delta;

                //a  d
                //c  b
                Vector3 c = new Vector3(a.x, b.y);
                Vector3 d = new Vector3(b.x, a.y);
                Vector2[] corners = new Vector2[] { a, d, b, c };
                Vector2[] corners2 = PolygonUtility.Clockwise(corners);
                corners2 = PolygonUtility.Extrude(corners2, Mathf.Sqrt(s_Radius * s_Radius * 2));


                Vector2[] circle = CreateCircle(a);
                int roundPointCount = (int)Mathf.Max(circle.Length / 2f - 1, 1);

                Vector2[] polygon = circle;


                if (!currentEvent.alt)
                {
                    polygon = PolygonUtility.RoundCorner2(corners2, roundPointCount, s_Radius);
                }

                Handles.DrawAAConvexPolygon(VectorUtility.ConvertToVector3(polygon));

                if (currentEvent.alt)
                {
                    b = a;
                    s_Radius = Vector2.Distance(s_PrevMousePosition, mousePosition);
                }
                else
                {

                }

                Handles.DrawLine(b, mousePosition);

                if (paint)
                {
                    using (new EditScope(m_Terrain2DEditor))
                    {
                        Terrain2D terrain = m_Terrain2DEditor.target as Terrain2D;

                        if (terrain is PolygonTerrain2D)
                        {
                            s_Apply |= terrain.EditByPolygon(polygon, s_Fill) > 0;
                        }
                        else
                        {
                            VoxelTerrain2D voxelTerrain = terrain as VoxelTerrain2D;

                            polygon = PolygonUtility.BevelCorner2(corners2, s_Radius);

                            s_Apply |= voxelTerrain.EditByPolygon(polygon, s_Fill) > 0;
                            foreach (var corner in corners)
                            {
                                s_Apply |= voxelTerrain.EditByCircle(corner, s_Radius, s_Fill) > 0;
                            }
                        }
                    }

                    if (s_Apply) Apply();
                }

                SceneView.RepaintAll();
            }

            Vector2[] CreateCircle(Vector2 center)
            {
                Terrain2D terrain = m_Terrain2DEditor.target as Terrain2D;

                if (terrain is PolygonTerrain2D)
                {
                    return PolygonUtility.CreateCircle(center, s_Radius, m_Terrain2DEditor.m_SimplificationProp.floatValue);
                }

                return PolygonUtility.CreateCircle(center, 50, s_Radius);
            }

            void Apply()
            {
                s_Apply = false;
                Terrain2D terrain = m_Terrain2DEditor.target as Terrain2D;
                Terrain2DAsset asset = (Terrain2DAsset)m_Terrain2DEditor.m_AssetProp.objectReferenceValue;
                Undo.RegisterCompleteObjectUndo(asset, "Edit Terrain2DAsset");
                terrain.WriteAsset(asset);
                EditorUtility.SetDirty(asset);
                EditorUtility.SetDirty(terrain);
            }

            class EditScope : IDisposable
            {
                Terrain2DEditor m_Terrain2DEditor;
                bool m_IsDiggable;
                bool m_IsFillable;

                public EditScope(Terrain2DEditor terrain2DEditor)
                {
                    m_Terrain2DEditor = terrain2DEditor;
                    m_IsDiggable = m_Terrain2DEditor.m_IsDiggableProp.boolValue;
                    m_IsFillable = m_Terrain2DEditor.m_IsFillableProp.boolValue;

                    m_Terrain2DEditor.m_IsDiggableProp.boolValue = true;
                    m_Terrain2DEditor.m_IsFillableProp.boolValue = true;
                    m_Terrain2DEditor.serializedObject.ApplyModifiedPropertiesWithoutUndo();
                }

                public void Dispose()
                {
                    m_Terrain2DEditor.m_IsDiggableProp.boolValue = m_IsDiggable;
                    m_Terrain2DEditor.m_IsFillableProp.boolValue = m_IsFillable;
                    m_Terrain2DEditor.serializedObject.ApplyModifiedPropertiesWithoutUndo();
                }
            }
        }
    }


    [CustomEditor(typeof(Terrain2DAsset), true)]
    class Terrain2DAssetEditor : Editor
    {
        [MenuItem("Assets/Diggable Terrains 2D/File To Asset")]
        static void FileToAsset()
        {
            var objects = Selection.objects;
            foreach (var obj in objects)
            {
                string path = AssetDatabase.GetAssetPath(obj);
                if (path != "")
                {
                    string absolutePath = FileUtility.GetAbsolutePath(path);
                    if (Terrain2D.ValidatePath(absolutePath))
                    {
                        var asset = Terrain2D.CreateAssetFromFile(absolutePath);
                        AssetDatabase.CreateAsset(asset, path + ".Asset");
                        AssetDatabase.Refresh();
                    }
                }
            }
        }

        [MenuItem("Assets/Diggable Terrains 2D/File To Asset", validate = true)]
        static bool FileToAssetValidate()
        {
           var objects = Selection.objects;
            foreach (var obj in objects)
            {
                string path = AssetDatabase.GetAssetPath(obj);
                if (path != "")
                {
                    path = FileUtility.GetAbsolutePath(path);
                    if (Terrain2D.ValidatePath(path)) 
                    {
                        return true;
                    }
                }
            }
            return false;
        }


        public override void OnInspectorGUI()
        {

        }
    }
}