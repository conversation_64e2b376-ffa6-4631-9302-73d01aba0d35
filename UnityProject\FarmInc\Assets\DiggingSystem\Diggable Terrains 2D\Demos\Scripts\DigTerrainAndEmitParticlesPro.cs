/*
This is a more complex version of the DigTerrainAndEmitParticles example, which emits particles in a better way.
*/

using UnityEngine;
using ScriptBoy.DiggableTerrains2D;
using System.Collections.Generic;

namespace ScriptBoy.DiggableTerrains2D_Demos
{
    [RequireComponent(typeof(Shovel))]
    public class DigTerrainAndEmitParticlesPro : MonoBehaviour
    {
        [SerializeField] Shovel m_Shovel;

        [Space]
        [SerializeField] AudioSource m_AudioSource;
        [SerializeField] AudioClip m_DigSound;

        [Space]
        [SerializeField] ParticleSystem m_Dirt;
        [SerializeField] int m_DirtParticleCount;

        static List<Vector2> s_Particles = new List<Vector2>();
        static int s_DirtParticleSeed;

        float m_DigSoundDelay;

        void Start()
        {
            m_Dirt.Play();
        }

        void Update()
        {
            transform.position = (Vector2)Camera.main.ScreenToWorldPoint(Input.mousePosition);

            if (Input.GetMouseButton(0))
            {
                Dig();
            }
        }

        void Dig()
        {
            TerrainParticleUtility.GetParticles(m_Shovel, s_Particles, m_DirtParticleCount, s_DirtParticleSeed);

            if (m_Shovel.Dig(out float diggedArea) && diggedArea > 0.01f)
            {
                if (m_DigSoundDelay < 0)
                {
                    m_DigSoundDelay = 0.15f;
                    m_AudioSource.pitch = Random.Range(0.8f, 1.2f);
                    m_AudioSource.PlayOneShot(m_DigSound, Random.Range(0.2f, 0.5f));
                }


                int n = s_Particles.Count;
                for (int i = 0; i < n; i++)
                {
                    ParticleSystem.EmitParams emit = new ParticleSystem.EmitParams();
                    emit.position = s_Particles[i];
                    m_Dirt.Emit(emit, 1);
                }

                s_DirtParticleSeed++;
            }

            m_DigSoundDelay -= Time.deltaTime;
        }
    }
}