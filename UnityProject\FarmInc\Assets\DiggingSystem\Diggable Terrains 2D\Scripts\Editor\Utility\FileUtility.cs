using System.Reflection;
using UnityEditor;
using UnityEngine;

namespace ScriptBoy.DiggableTerrains2D
{
    static class FileUtility
    {
        public static string ActiveFolderPath()
        {
            var type = typeof(ProjectWindowUtil);
            var getActiveFolderPath = type.GetMethod("GetActiveFolderPath", BindingFlags.Static | BindingFlags.NonPublic);
            string path = (string)getActiveFolderPath.Invoke(null, new object[0]);
            if (path == null)
            {
                path = Application.dataPath;
            }
            return path;
        }

        public static string GetRelativePath(string path)
        {
            // return System.IO.Path.GetRelativePath(Application.dataPath, path);

            //  return path.Remove(0, Application.dataPath.Length - "Assets/".Length - 1);
            path.Replace('\\','/');
            return FileUtil.GetProjectRelativePath(path);
        }

        public static string GetAbsolutePath(string path)
        {
            return Application.dataPath + path.Remove(0, 6);
        }
    }
}