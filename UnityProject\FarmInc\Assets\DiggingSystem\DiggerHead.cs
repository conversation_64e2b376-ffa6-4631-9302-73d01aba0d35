using UnityEngine;
using System.Collections;
using Lean.Pool;
using DG.Tweening;
using Sirenix.OdinInspector;
using GameWish.Game;
using Qarth;
using System.Collections.Generic;
public enum DiggerState
{
	None = 0,
	Dig = 1,
	Cleaner,
}
public class DiggerHead : MonoBehaviour
{
	public float moveSpeed = 5f;
	float createPieceInterval = 0.05f;
	private DiggerPiece m_PiecePre;
	private List<DiggerPiece> m_LstPiecePre = new List<DiggerPiece>();
	public DiggerState state = DiggerState.None;
	public GameObject goDigger;
	public GameObject goCleaner;
	public Rigidbody2D rigidbody2D;
	public DigRope digRope;

	public string m_JoystickName = "Movement";
	private Vector3 m_InputVec;
	private float m_WalkSpdRatio = 0.2f;


	private void Start()
	{
		for (int i = 0; i < 5; i++)
		{
			string pre = $"StonePiece{i + 1}";
			AddressableResMgr.S.LoadAssetAsyncByName<GameObject>(pre, (obj, state) =>
			{
				if (state)
				{
					m_LstPiecePre.Add(obj.GetComponent<DiggerPiece>());
				}
			});
		}
		Change2Dig();
		UltimateJoystick.EnableJoystick(m_JoystickName);
	}

	void Update()
	{
		if (UltimateJoystick.GetJoystickState(m_JoystickName))
		{
			m_InputVec = new Vector3(UltimateJoystick.GetHorizontalAxis(m_JoystickName), UltimateJoystick.GetVerticalAxis(m_JoystickName), 0);
		}
		else
		{
			m_InputVec = Vector3.zero;
		}

		if (m_InputVec != Vector3.zero)
		{
			if (m_InputVec.magnitude < Define.PLAYER_JOYSTICK_RUN_THRESHOLD)
			{
				m_InputVec = m_InputVec.normalized * m_WalkSpdRatio;
			}
			else
			{
				m_InputVec = m_InputVec.normalized;
			}

			// 更新挖掘头的位置
			transform.position += m_InputVec.normalized * Time.deltaTime * moveSpeed;
			goDigger.transform.localEulerAngles += Vector3.forward * Time.deltaTime * 1000;
			if (state == DiggerState.Dig)
			{
				//超过间隔就生成碎片
				createPieceInterval -= Time.deltaTime;
				if (DigGameMgr.S.Dig())
				{
					if (createPieceInterval < 0)
					{
						createPieceInterval = 0.1f;
						CreatePiece();
					}
				}
			}
			else if (state == DiggerState.Cleaner)
			{
				//吸头根据摇杆方向旋转
				//goCleaner.transform.localEulerAngles = Vector3.forward * Mathf.Atan2(m_InputVec.y, m_InputVec.x) * Mathf.Rad2Deg;
				//吸附附近范围内的碎片
				var pieces = GameObject.FindGameObjectsWithTag("DiggingPiece");
				foreach (var item in pieces)
				{
					var dirPiece = item.transform.position - this.transform.position;
					if (dirPiece.magnitude < 1f)
					{
						//吸附过来再回收
						var piece = item.GetComponent<DiggerPiece>();
						piece.Move2Head(this.transform.position);
					}
				}
			}

			digRope.ReCaculateRope();
		}
	}

	public void CreatePiece()
	{
		m_PiecePre = m_LstPiecePre.GetRandomLItem();
		var go = LeanPool.Spawn(m_PiecePre, transform.position, Quaternion.Euler(Vector3.zero));
		go.transform.localScale = Vector3.one * Random.Range(0.5f, 1f);

		EffectControl.S.PlayPosEffect("Hit_stone", this.transform.position);
	}

	[Button("Change2Dig")]
	public void Change2Dig()
	{
		state = DiggerState.Dig;
		goDigger.SetActive(true);
		goCleaner.SetActive(false);
		rigidbody2D.isKinematic = true;
	}
	[Button("Change2Cleaner")]
	public void Change2Cleaner()
	{
		state = DiggerState.Cleaner;
		goDigger.SetActive(false);
		goCleaner.SetActive(true);
		rigidbody2D.isKinematic = false;
	}


}