using UnityEngine;
using System.Collections;
using Lean.Pool;
using DG.Tweening;
using Sirenix.OdinInspector;
using GameWish.Game;
using Qarth;
using System.Collections.Generic;
using ScriptBoy.DiggableTerrains2D;
public enum DiggerState
{
	None = 0,
	Dig = 1,
	Cleaner,
}
public class DiggerHead : MonoBehaviour
{
	public float moveSpeed = 5f;
	private float basicSpeed = 5f;
	float createPieceInterval = 0.05f;
	float createEffectInterval = 0.2f;
	private DiggerPiece m_PiecePre;
	[SerializeField] private Shovel m_Shovel;
	[SerializeField] private Transform m_EffectDigger;
	[SerializeField] private Transform m_EffectCleaner;
	private List<DiggerPiece> m_LstPiecePre = new List<DiggerPiece>();
	public DiggerState state = DiggerState.None;
	public GameObject goDigger;
	public GameObject goCleaner;
	public Rigidbody2D rigidbody2D;
	public DigRope digRope;
	public bool digging = false;
	public int energy = 100;
	public int curPower;
	private int m_CurLevelNeedPower;



	public string m_JoystickName = "Movement";
	private Vector3 m_InputVec;
	private float m_WalkSpdRatio = 0.2f;
	private ParticleSystem[] m_DiggerEffs;
	private ParticleSystem[] m_CleanerEffs;

	private int m_CurCreateStoneCount = 0;


	private TDDigUpgradeInfo m_UpgradeData;

	public void SetData(TDDigUpgradeInfo upgradeData)
	{
		m_UpgradeData = upgradeData;
		curPower = m_UpgradeData.diggerPower;
		SetCurLevelPower(curPower);
	}


	private void Start()
	{
		for (int i = 0; i < 5; i++)
		{
			string pre = $"StonePiece{i + 1}";
			AddressableResMgr.S.LoadAssetAsyncByName<GameObject>(pre, (obj, state) =>
			{
				if (state)
				{
					m_LstPiecePre.Add(obj.GetComponent<DiggerPiece>());
				}
			});
		}
		m_DiggerEffs = m_EffectDigger.GetComponentsInChildren<ParticleSystem>();
		m_CleanerEffs = m_EffectCleaner.GetComponentsInChildren<ParticleSystem>();
		Change2Dig();
		UltimateJoystick.EnableJoystick(m_JoystickName);
	}

	void Update()
	{

		if (UltimateJoystick.GetJoystickState(m_JoystickName))
		{
			m_InputVec = new Vector3(UltimateJoystick.GetHorizontalAxis(m_JoystickName), UltimateJoystick.GetVerticalAxis(m_JoystickName), 0);
		}
		else
		{
			m_InputVec = Vector3.zero;
		}

		if (m_InputVec != Vector3.zero)
		{
			if (m_InputVec.magnitude < Define.PLAYER_JOYSTICK_RUN_THRESHOLD)
			{
				m_InputVec = m_InputVec.normalized * m_WalkSpdRatio;
			}
			else
			{
				m_InputVec = m_InputVec.normalized;
			}

			// if (energy <= 0)
			// {
			// 	return;
			// }

			goDigger.transform.localEulerAngles += Vector3.forward * Time.deltaTime * 500;
			if (state == DiggerState.Dig)
			{
				//超过间隔就生成碎片
				digging = Dig();
				if (digging)
				{
					transform.position += m_InputVec.normalized * Time.deltaTime * moveSpeed;
				}
				else
				{
					transform.position += m_InputVec.normalized * Time.deltaTime * basicSpeed;
				}
				PlayEffects(true);
			}
			else if (state == DiggerState.Cleaner)
			{
				// 更新挖掘头的位置
				transform.position += m_InputVec.normalized * Time.deltaTime * basicSpeed;
				digging = false;
				//吸头根据摇杆方向旋转
				goCleaner.transform.localEulerAngles = Vector3.forward * (Mathf.Atan2(m_InputVec.y, m_InputVec.x) * Mathf.Rad2Deg + 90);
				//吸附附近范围内的碎片
				var pieces = GameObject.FindGameObjectsWithTag("DiggingPiece");
				foreach (var item in pieces)
				{
					var dirPiece = item.transform.position - this.transform.position;
					if (dirPiece.magnitude < 2f)
					{
						//吸附过来再回收
						var piece = item.GetComponent<DiggerPiece>();
						piece.Move2Head(this.transform.position);
						energy--;
					}
				}
				PlayEffects(true);
			}
			else
			{
				transform.position += m_InputVec.normalized * Time.deltaTime * moveSpeed;
			}

			digRope.ReCaculateRope();
		}
		else
		{
			PlayEffects(false);
		}
	}

	public void SetCurLevelPower(int needPower)
	{
		m_CurLevelNeedPower = needPower;
		moveSpeed = ((float)curPower / m_CurLevelNeedPower) * basicSpeed;//Mathf.Pow((curPower / m_CurLevelNeedPower), -3) * basicSpeed;
																		 //Log.e("{0}  {1}   {2}", curPower, m_CurLevelNeedPower, moveSpeed);
	}

	public void CreatePiece()
	{
		m_PiecePre = m_LstPiecePre.GetRandomLItem();
		var go = LeanPool.Spawn(m_PiecePre, transform.position, Quaternion.Euler(Vector3.zero), DigGameMgr.S.effectRoot);
		go.transform.localScale = Vector3.one * Random.Range(0.6f, 1f);

	}


	public void Change2Dig()
	{
		state = DiggerState.Dig;
		goDigger.SetActive(true);
		goCleaner.SetActive(false);
		rigidbody2D.isKinematic = false;
		m_PlayEffect = true;
		PlayEffects(false);
	}

	public void Change2Cleaner()
	{
		state = DiggerState.Cleaner;
		goDigger.SetActive(false);
		goCleaner.SetActive(true);
		rigidbody2D.isKinematic = false;
		m_PlayEffect = true;
		PlayEffects(false);
	}

	private bool m_PlayEffect = false;
	private void PlayEffects(bool show)
	{
		var eff = state == DiggerState.Dig ? m_DiggerEffs : m_CleanerEffs;
		if (m_PlayEffect == show)
		{
			return;
		}
		m_PlayEffect = show;
		if (show)
		{
			foreach (var item in eff)
			{
				item.Play();
			}
		}
		else
		{
			foreach (var item in eff)
			{
				item.Stop();
			}
		}
	}

	private bool Dig()
	{
		// 使用-1作为Layer Mask确保所有地形都被包含
		if (m_Shovel.Dig(out float diggedArea, -1) && diggedArea > 0.01f)
		{
			DigGameMgr.S.curDigArea += diggedArea;
			int createCount = (int)DigGameMgr.S.curDigArea;
			if (createCount > m_CurCreateStoneCount)
			{
				int createNum = createCount - m_CurCreateStoneCount;
				for (int i = 0; i < createNum; i++)
				{
					CreatePiece();
					energy--;
				}
				m_CurCreateStoneCount = createCount;
			}
			createEffectInterval -= Time.deltaTime;
			if (createEffectInterval < 0)
			{
				createEffectInterval = 0.2f;
				EffectControl.S.PlayPosEffect("Hit_stone", this.transform.position);
			}
			return true;
		}
		return false;

	}


}