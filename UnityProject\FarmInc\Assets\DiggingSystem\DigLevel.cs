using UnityEngine;
using System.Collections;
using Lean.Pool;
using DG.Tweening;
using GameWish.Game;
using Qarth;
using System.Collections.Generic;


public class DigLevel : MonoBehaviour
{
	public TDDigLevelInfo levelInfo;
	public List<Transform> lstMinePos;
	private BoxCollider2D m_Collider;

	public int level;

	public void Init(TDDigLevelInfo tdData)
	{
		level = tdData.id;
		levelInfo = tdData;
		EventSystem.S.Register(DigEvt.OnDigLevelChange, OnDigLevelChange);
		if (m_Collider == null)
			m_Collider = GetComponent<BoxCollider2D>();
		OnDigLevelChange(0);
	}

	public void Close()
	{
		EventSystem.S.UnRegister(DigEvt.OnDigLevelChange, OnDigLevelChange);
	}

	private void OnTriggerEnter2D(Collider2D collision)
	{
		if (levelInfo == null) return;
		//如果检测到刀头，修改刀头的速度
		if (collision.transform.CompareTag("DigHead"))
		{
			var head = collision.transform.GetComponent<DiggerHead>();
			head?.SetCurLevelPower(levelInfo.diggerPower);
		}
	}

	private void OnDigLevelChange(int key, params object[] args)
	{
		if (DigGameMgr.S.curMaxLevel >= level)
		{
			m_Collider.isTrigger = true;
		}
		else
		{
			m_Collider.isTrigger = false;
		}
	}
}