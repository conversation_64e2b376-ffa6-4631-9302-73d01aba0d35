using UnityEngine;
using ScriptBoy.DiggableTerrains2D;
using System.Collections.Generic;

/// <summary>
/// 增强版铲子，确保能在多个地形上正确工作
/// </summary>

namespace GameWish.Game
{
	public class MultiTerrainShovel : MonoBehaviour
	{
	    [Header("Shovel Settings")]
	    public Shape2D shape;
	    public float simplification = 0.001f;
	    public bool enableWave = false;
	    public float waveLength = 1f;
	    public float waveAmplitude = 0.5f;
	    
	    [Header("Debug")]
	    public bool enableDebugLog = false;
	    public bool showDigArea = false;
	    
	    /// <summary>
	    /// 增强版挖掘方法，确保所有相交的地形都被挖掘
	    /// </summary>
	    public bool Dig(out float totalDugArea)
	    {
	        totalDugArea = 0f;
	        
	        if (shape == null)
	        {
	            if (enableDebugLog) Debug.LogWarning("Shape is null!");
	            return false;
	        }
	        
	        // 获取挖掘多边形
	        Vector2[] polygon = GetPolygon();
	        if (polygon == null || polygon.Length < 3)
	        {
	            if (enableDebugLog) Debug.LogWarning("Invalid polygon!");
	            return false;
	        }
	        
	        // 计算挖掘区域的边界
	        Bounds digBounds = PolygonUtility.GetBounds(polygon);
	        
	        // 获取所有可能相交的地形
	        var allTerrains = GetIntersectingTerrains(digBounds);
	        
	        if (enableDebugLog)
	        {
	            Debug.Log($"找到 {allTerrains.Count} 个可能相交的地形");
	        }
	        
	        bool anyDug = false;
	        
	        // 对每个相交的地形进行挖掘
	        foreach (var terrain in allTerrains)
	        {
	            if (!terrain.isDiggable || !terrain.isBuilt)
	            {
	                if (enableDebugLog)
	                {
	                    Debug.Log($"跳过地形 {terrain.name}: 不可挖掘({!terrain.isDiggable}) 或未构建({!terrain.isBuilt})");
	                }
	                continue;
	            }
	            
	            float dugArea = 0f;
	            
	            try
	            {
	                // 尝试挖掘这个地形
	                if (IsCircleShape())
	                {
	                    CircleShape2D c = shape as CircleShape2D;
	                    float radius = c.radius * c.transform.lossyScale.x * transform.lossyScale.x;
	                    Vector2 position = c.transform.position + transform.position;
	                    dugArea = terrain.EditByCircle(position, radius, false);
	                }
	                else
	                {
	                    dugArea = terrain.EditByPolygon(polygon, false);
	                }
	                
	                if (dugArea > 0.001f)
	                {
	                    totalDugArea += dugArea;
	                    anyDug = true;
	                    
	                    if (enableDebugLog)
	                    {
	                        Debug.Log($"在地形 {terrain.name} 上挖掘了 {dugArea} 面积");
	                    }
	                }
	            }
	            catch (System.Exception e)
	            {
	                Debug.LogError($"挖掘地形 {terrain.name} 时出错: {e.Message}");
	            }
	        }
	        
	        if (enableDebugLog)
	        {
	            Debug.Log($"总挖掘面积: {totalDugArea}, 成功: {anyDug}");
	        }
	        
	        return anyDug;
	    }
	    
	    /// <summary>
	    /// 获取与挖掘区域相交的所有地形
	    /// </summary>
	    private List<Terrain2D> GetIntersectingTerrains(Bounds digBounds)
	    {
	        List<Terrain2D> intersectingTerrains = new List<Terrain2D>();
	        
	        // 获取所有激活的地形
	        var allTerrains = Terrain2D.activeInstances;
	        
	        foreach (var terrain in allTerrains)
	        {
	            if (terrain == null) continue;
	            
	            // 获取地形边界
	            Bounds terrainBounds = GetTerrainBounds(terrain);
	            
	            // 检查边界是否相交
	            if (terrainBounds.Intersects(digBounds))
	            {
	                intersectingTerrains.Add(terrain);
	                
	                if (enableDebugLog)
	                {
	                    Debug.Log($"地形 {terrain.name} 与挖掘区域相交");
	                    Debug.Log($"  地形边界: {terrainBounds}");
	                    Debug.Log($"  挖掘边界: {digBounds}");
	                }
	            }
	        }
	        
	        return intersectingTerrains;
	    }
	    
	    /// <summary>
	    /// 获取地形的边界
	    /// </summary>
	    private Bounds GetTerrainBounds(Terrain2D terrain)
	    {
	        // 尝试从渲染器获取边界
	        var renderers = terrain.GetComponentsInChildren<Renderer>();
	        if (renderers.Length > 0)
	        {
	            Bounds bounds = renderers[0].bounds;
	            for (int i = 1; i < renderers.Length; i++)
	            {
	                bounds.Encapsulate(renderers[i].bounds);
	            }
	            return bounds;
	        }
	        
	        // 如果没有渲染器，使用碰撞器
	        var colliders = terrain.GetComponentsInChildren<Collider2D>();
	        if (colliders.Length > 0)
	        {
	            Bounds bounds = colliders[0].bounds;
	            for (int i = 1; i < colliders.Length; i++)
	            {
	                bounds.Encapsulate(colliders[i].bounds);
	            }
	            return bounds;
	        }
	        
	        // 最后使用变换位置（给一个默认大小）
	        return new Bounds(terrain.transform.position, Vector3.one * 10f);
	    }
	    
	    /// <summary>
	    /// 获取挖掘多边形
	    /// </summary>
	    private Vector2[] GetPolygon()
	    {
	        if (shape == null) return null;
	        
	        Vector2[] polygon = shape.GetWorldPoints();
	        
	        if (enableWave && (shape is CircleShape2D || shape is BoxShape2D))
	        {
	            polygon = PolygonUtility.Remesh(polygon, waveLength / 5f, true);
	            polygon = PolygonUtility.Wave(polygon, waveLength, waveAmplitude, 0);
	        }
	        
	        polygon = PolygonSimplifier.Simplify(polygon, simplification, true);
	        return polygon;
	    }
	    
	    /// <summary>
	    /// 检查是否为圆形
	    /// </summary>
	    private bool IsCircleShape()
	    {
	        if (enableWave) return false;
	        if (!(shape is CircleShape2D)) return false;
	        if ((shape as CircleShape2D).pointCount >= 20) return false;
	        
	        Vector2 a = shape.transform.lossyScale;
	        Vector2 b = transform.lossyScale;
	        
	        return Mathf.Abs(a.x * b.x) == Mathf.Abs(a.y * b.y);
	    }
	    
	    void OnDrawGizmos()
	    {
	        if (!showDigArea || shape == null) return;
	        
	        Vector2[] polygon = GetPolygon();
	        if (polygon != null && polygon.Length > 2)
	        {
	            Gizmos.color = Color.yellow;
	            for (int i = 0; i < polygon.Length; i++)
	            {
	                int next = (i + 1) % polygon.Length;
	                Gizmos.DrawLine(polygon[i], polygon[next]);
	            }
	        }
	    }
	}
	
}